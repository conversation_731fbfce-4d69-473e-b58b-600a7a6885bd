# 表情控制功能 - LocalMediaPlayer (简化版)

**上下文版本号: v1.0**

## 🎯 功能概述

LocalMediaPlayer现在通过监听MediaPlayer状态自动控制EMotionPlayer的表情显示：
- **播放时**: 显示"音乐"表情 (0x07)
- **停止/暂停/播放完成时**: 显示"待机"表情 (0x09)

## 🎭 表情映射

### 自动表情控制
```java
private static final String MUSIC_EMOTION = "音乐";  // 播放时的表情
private static final String IDLE_EMOTION = "待机";   // 待机时的表情
```

### 表情代码对应
- **"音乐"** → 0x07 (音乐表情)
- **"待机"** → 0x09 (待机表情)

## 🔄 自动控制逻辑

### MediaPlayer监听器触发
```java
// 播放准备完成并开始时
mediaPlayer.setOnPreparedListener(mp -> {
    mp.start();
    setEmotionForMediaPlayerState(true); → "音乐"表情
});

// 播放完成时
mediaPlayer.setOnCompletionListener(mp -> {
    setEmotionForMediaPlayerState(false); → "待机"表情
});
```

### 核心表情控制方法
```java
private void setEmotionForMediaPlayerState(boolean isPlaying) {
    String targetEmotion = isPlaying ? MUSIC_EMOTION : IDLE_EMOTION;

    // 避免重复设置相同表情
    if (targetEmotion.equals(currentEmotion)) {
        return; // 跳过重复设置
    }

    EMotionPlayer.playEMotionByDescription(targetEmotion);
    currentEmotion = targetEmotion;
}
```

## 🎛️ 简化设计

### 自动工作
- **无需手动控制** - 完全基于MediaPlayer状态自动工作
- **无需IoT方法** - 移除了复杂的控制接口
- **无需配置** - 开箱即用，自动监听播放状态

### 工作原理
```java
// 系统自动监听MediaPlayer状态变化
// 无需任何手动调用，表情会自动跟随播放状态变化
```

## 📊 状态管理

### 内部状态变量
```java
private String currentEmotion = "待机";          // 当前表情状态
private static final String MUSIC_EMOTION = "音乐";  // 播放时的表情
private static final String IDLE_EMOTION = "待机";   // 待机时的表情
```

### 状态同步
- **启动时**: 自动设置为"待机"表情
- **播放变化**: 通过MediaPlayer监听器自动同步
- **完全自动**: 无需任何手动干预

## 🔄 完整工作流程

### 场景1: 播放故事
```
1. 用户: "播放小红帽的故事"
2. LocalMediaPlayer.playCurrentItem()
3. MediaPlayer准备完成 → OnPreparedListener触发
4. 自动调用: setEmotionForMediaPlayerState(true)
5. EMotionPlayer.playEMotionByDescription("音乐")
6. 表情显示: 音乐表情 (0x07)
```

### 场景2: 播放完成
```
1. 故事播放到结尾
2. MediaPlayer播放完成 → OnCompletionListener触发
3. 自动调用: setEmotionForMediaPlayerState(false)
4. EMotionPlayer.playEMotionByDescription("待机")
5. 表情显示: 待机表情 (0x09)
```

### 场景3: 暂停/停止播放
```
1. 用户: "暂停播放" 或 "停止播放"
2. LocalMediaPlayer.pause() 或 stop()
3. 播放状态变为非播放状态
4. 下次播放时会重新触发OnPreparedListener
5. 表情会自动同步到正确状态
```

## 🎯 核心特性

### 1. **智能重复检测**
- 避免重复设置相同表情
- 减少不必要的硬件调用
- 提升性能和稳定性

### 2. **异常处理**
```java
try {
    EMotionPlayer.playEMotionByDescription(targetEmotion);
    currentEmotion = targetEmotion;
    Log.i(TAG, "表情设置成功: " + targetEmotion);
} catch (Exception e) {
    Log.e(TAG, "设置表情失败: " + targetEmotion, e);
}
```

### 3. **MediaPlayer监听器集成**
```java
// 播放准备完成监听器
mediaPlayer.setOnPreparedListener(mp -> {
    // 播放开始时自动设置音乐表情
    setEmotionForMediaPlayerState(true);
});

// 播放完成监听器
mediaPlayer.setOnCompletionListener(mp -> {
    // 播放完成时自动设置待机表情
    setEmotionForMediaPlayerState(false);
});
```

## 🔍 调试信息

### 关键日志
```
LocalMediaPlayer: 初始化表情状态为待机
LocalMediaPlayer: 表情状态初始化完成: 待机
LocalMediaPlayer: MediaPlayer状态变化，设置表情: 待机 -> 音乐
LocalMediaPlayer: 表情设置成功: 音乐
LocalMediaPlayer: MediaPlayer状态变化，设置表情: 音乐 -> 待机
LocalMediaPlayer: 表情设置成功: 待机
```

## ⚙️ 配置选项

### 表情映射自定义
```java
// 可以修改表情映射
private static final String MUSIC_EMOTION = "开心";  // 改为开心表情
private static final String IDLE_EMOTION = "思考";   // 改为思考表情
```

### 启用/禁用控制
```java
// 默认启用
private boolean emotionControlEnabled = true;

// 可以通过IoT方法动态控制
localMediaPlayer.setEmotionControl("false"); // 禁用
localMediaPlayer.setEmotionControl("true");  // 启用
```

## 📊 性能影响

### 资源使用
- **内存开销**: 微不足道（几个字符串变量）
- **CPU使用**: 极小（简单的状态判断）
- **硬件调用**: 仅在状态变化时调用，避免重复

### 响应速度
- **表情切换**: 实时响应播放状态变化
- **重复检测**: 避免不必要的硬件调用
- **异常处理**: 确保表情控制不影响播放功能

## 🎉 用户体验

### 视觉反馈
- 🎵 **播放时**: 显示音乐表情，用户直观看到播放状态
- 😴 **待机时**: 显示待机表情，表示系统空闲
- 🔄 **状态同步**: 表情与MediaPlayer状态完全同步

### 智能交互
- **完全自动**: 无需任何手动干预，表情自动跟随播放状态
- **可靠监听**: 基于MediaPlayer原生监听器，状态同步准确
- **简洁设计**: 移除复杂的控制接口，专注核心功能

## ⚠️ 注意事项

1. **EMotionPlayer依赖**: 确保EMotionPlayer正常工作
2. **表情描述**: 使用正确的表情描述字符串
3. **异常处理**: 表情设置失败不会影响播放功能
4. **状态同步**: 启动时会自动设置为待机表情
5. **重复检测**: 避免频繁设置相同表情

## 🔧 扩展建议

### 更多表情映射
```java
// 可以根据不同内容类型设置不同表情
switch (currentCategory) {
    case "故事":
        targetEmotion = "专注";
        break;
    case "儿歌":
        targetEmotion = "开心";
        break;
    case "古诗":
        targetEmotion = "思考";
        break;
    default:
        targetEmotion = "音乐";
}
```

### 动态表情
```java
// 可以根据播放进度动态调整表情
private void updateEmotionByProgress(int progress) {
    if (progress > 80) {
        setEmotion("满足"); // 快结束时显示满足表情
    }
}
```

## 🎯 总结

通过简化的表情控制功能：

- 🎭 **视觉反馈** - 表情与MediaPlayer状态完全同步
- 🤖 **智能控制** - 基于MediaPlayer监听器自动切换表情
- 🔧 **简洁设计** - 移除复杂接口，专注核心功能
- 📊 **性能优化** - 智能重复检测，避免不必要调用
- 🎯 **可靠性高** - 直接监听MediaPlayer状态，更加准确

现在小萝卜机器人的表情会根据音乐播放状态自动变化：
- 播放时显示生动的音乐表情 🎵
- 停止/完成时显示安静的待机表情 😴

这种基于MediaPlayer监听器的设计更加简洁、可靠，为用户提供准确的视觉反馈！🤖✨
