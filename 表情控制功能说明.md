# 表情控制功能 - LocalMediaPlayer

**上下文版本号: v1.0**

## 🎯 功能概述

LocalMediaPlayer现在可以根据播放状态自动控制EMotionPlayer的表情显示：
- **播放时**: 显示"音乐"表情 (0x07)
- **停止/暂停时**: 显示"待机"表情 (0x09)

## 🎭 表情映射

### 自动表情控制
```java
private static final String MUSIC_EMOTION = "音乐";  // 播放时的表情
private static final String IDLE_EMOTION = "待机";   // 待机时的表情
```

### 表情代码对应
- **"音乐"** → 0x07 (音乐表情)
- **"待机"** → 0x09 (待机表情)

## 🔄 自动控制逻辑

### 播放状态变化触发
```java
// 播放开始时
mediaPlayer.start() → setEmotionForPlaybackState(true) → "音乐"表情

// 暂停时
mediaPlayer.pause() → setEmotionForPlaybackState(false) → "待机"表情

// 停止时
mediaPlayer.stop() → setEmotionForPlaybackState(false) → "待机"表情

// 恢复播放时
mediaPlayer.resume() → setEmotionForPlaybackState(true) → "音乐"表情
```

### 智能重复检测
```java
private void setEmotionForPlaybackState(boolean isPlaying) {
    String targetEmotion = isPlaying ? MUSIC_EMOTION : IDLE_EMOTION;
    
    // 避免重复设置相同表情
    if (targetEmotion.equals(currentEmotion)) {
        return; // 跳过重复设置
    }
    
    EMotionPlayer.playEMotionByDescription(targetEmotion);
    currentEmotion = targetEmotion;
}
```

## 🎛️ 控制接口

### IoT方法
- `setEmotionControl(enabled)` - 启用/禁用表情控制
- `setEmotion(emotion)` - 手动设置表情
- `syncEmotionState()` - 同步表情状态
- `getEmotionInfo()` - 获取表情控制信息

### 使用示例
```java
// 启用表情控制
localMediaPlayer.setEmotionControl("true");

// 禁用表情控制
localMediaPlayer.setEmotionControl("false");

// 手动设置表情
localMediaPlayer.setEmotion("开心");

// 同步表情状态（根据当前播放状态设置表情）
localMediaPlayer.syncEmotionState();

// 查看表情控制信息
localMediaPlayer.getEmotionInfo();
```

## 📊 状态管理

### 内部状态变量
```java
private boolean emotionControlEnabled = true;    // 是否启用表情控制
private String currentEmotion = "待机";          // 当前表情状态
```

### 状态同步
- **启动时**: 自动设置为"待机"表情
- **播放变化**: 自动同步表情状态
- **手动控制**: 支持手动设置任意表情

## 🔄 完整工作流程

### 场景1: 播放故事
```
1. 用户: "播放小红帽的故事"
2. LocalMediaPlayer.playCurrentItem()
3. 播放开始 → setEmotionForPlaybackState(true)
4. EMotionPlayer.playEMotionByDescription("音乐")
5. 表情显示: 音乐表情 (0x07)
```

### 场景2: 暂停播放
```
1. 用户: "暂停一下"
2. LocalMediaPlayer.pause()
3. 播放暂停 → setEmotionForPlaybackState(false)
4. EMotionPlayer.playEMotionByDescription("待机")
5. 表情显示: 待机表情 (0x09)
```

### 场景3: 恢复播放
```
1. 用户: "继续播放"
2. LocalMediaPlayer.resume()
3. 播放恢复 → setEmotionForPlaybackState(true)
4. EMotionPlayer.playEMotionByDescription("音乐")
5. 表情显示: 音乐表情 (0x07)
```

### 场景4: 停止播放
```
1. 用户: "停止播放"
2. LocalMediaPlayer.stop()
3. 播放停止 → setEmotionForPlaybackState(false)
4. EMotionPlayer.playEMotionByDescription("待机")
5. 表情显示: 待机表情 (0x09)
```

## 🎯 特殊功能

### 1. **智能重复检测**
- 避免重复设置相同表情
- 减少不必要的硬件调用
- 提升性能和稳定性

### 2. **异常处理**
```java
try {
    EMotionPlayer.playEMotionByDescription(targetEmotion);
    currentEmotion = targetEmotion;
    Log.i(TAG, "表情设置成功: " + targetEmotion);
} catch (Exception e) {
    Log.e(TAG, "设置表情失败: " + targetEmotion, e);
}
```

### 3. **开关控制**
```java
public void setEmotionControlEnabled(boolean enabled) {
    emotionControlEnabled = enabled;
    if (!enabled) {
        currentEmotion = IDLE_EMOTION; // 禁用时设置为待机
    }
}
```

### 4. **手动控制**
```java
public void setEmotion(String emotion) {
    if (!emotionControlEnabled) {
        Log.w(TAG, "表情控制已禁用，无法手动设置表情");
        return;
    }
    EMotionPlayer.playEMotionByDescription(emotion);
    currentEmotion = emotion;
}
```

## 🔍 调试信息

### 关键日志
```
LocalMediaPlayer: 初始化表情状态为待机
LocalMediaPlayer: 表情状态初始化完成: 待机
LocalMediaPlayer: 设置表情: 待机 -> 音乐
LocalMediaPlayer: 表情设置成功: 音乐
LocalMediaPlayer: 设置表情: 音乐 -> 待机
LocalMediaPlayer: 表情设置成功: 待机
```

### 状态查询
```java
// 查看表情控制信息
localMediaPlayer.getEmotionInfo();
// 输出: "表情控制状态 - 启用:是, 当前表情:音乐, 播放状态:播放中"
```

## ⚙️ 配置选项

### 表情映射自定义
```java
// 可以修改表情映射
private static final String MUSIC_EMOTION = "开心";  // 改为开心表情
private static final String IDLE_EMOTION = "思考";   // 改为思考表情
```

### 启用/禁用控制
```java
// 默认启用
private boolean emotionControlEnabled = true;

// 可以通过IoT方法动态控制
localMediaPlayer.setEmotionControl("false"); // 禁用
localMediaPlayer.setEmotionControl("true");  // 启用
```

## 📊 性能影响

### 资源使用
- **内存开销**: 微不足道（几个字符串变量）
- **CPU使用**: 极小（简单的状态判断）
- **硬件调用**: 仅在状态变化时调用，避免重复

### 响应速度
- **表情切换**: 实时响应播放状态变化
- **重复检测**: 避免不必要的硬件调用
- **异常处理**: 确保表情控制不影响播放功能

## 🎉 用户体验

### 视觉反馈
- 🎵 **播放时**: 显示音乐表情，用户直观看到播放状态
- 😴 **待机时**: 显示待机表情，表示系统空闲
- 🔄 **状态同步**: 表情与播放状态完全同步

### 智能交互
- **自动控制**: 无需手动干预，表情自动跟随播放状态
- **手动控制**: 支持手动设置特殊表情
- **开关控制**: 可以完全禁用表情控制

## ⚠️ 注意事项

1. **EMotionPlayer依赖**: 确保EMotionPlayer正常工作
2. **表情描述**: 使用正确的表情描述字符串
3. **异常处理**: 表情设置失败不会影响播放功能
4. **状态同步**: 启动时会自动设置为待机表情
5. **重复检测**: 避免频繁设置相同表情

## 🔧 扩展建议

### 更多表情映射
```java
// 可以根据不同内容类型设置不同表情
switch (currentCategory) {
    case "故事":
        targetEmotion = "专注";
        break;
    case "儿歌":
        targetEmotion = "开心";
        break;
    case "古诗":
        targetEmotion = "思考";
        break;
    default:
        targetEmotion = "音乐";
}
```

### 动态表情
```java
// 可以根据播放进度动态调整表情
private void updateEmotionByProgress(int progress) {
    if (progress > 80) {
        setEmotion("满足"); // 快结束时显示满足表情
    }
}
```

## 🎯 总结

通过表情控制功能：

- 🎭 **视觉反馈** - 表情与播放状态完全同步
- 🤖 **智能控制** - 自动根据播放状态切换表情
- 🎛️ **灵活配置** - 支持启用/禁用和手动控制
- 📊 **性能优化** - 智能重复检测，避免不必要调用
- 🔧 **易于扩展** - 可以轻松添加更多表情映射

现在小萝卜机器人的表情会根据音乐播放状态自动变化，为用户提供更加生动和直观的交互体验！🤖✨
