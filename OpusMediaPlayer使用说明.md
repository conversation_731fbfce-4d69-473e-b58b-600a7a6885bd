# OpusMediaPlayer - 基于MediaPlayer的Opus音频播放器

**上下文版本号: v1.0**

## 🎯 设计目标

使用MediaPlayer播放opus音频，提供更好的音频回声处理，替代AudioManager的播放方式。

## 🔧 核心特性

### 1. **使用现有的Opus处理库**
- ✅ 复用 `OpusJniHandler` 进行opus解码
- ✅ 支持24kHz采样率解码（更高音质）
- ✅ 自动处理opus到PCM的转换

### 2. **MediaPlayer播放**
- ✅ 使用MediaPlayer播放解码后的PCM音频
- ✅ 更好的音频回声处理和音质
- ✅ 专门的语音播放音频属性设置

### 3. **智能音频管理**
- ✅ 实时接收opus数据流
- ✅ 动态生成临时WAV文件
- ✅ 自动播放管理和状态通知

## 📋 技术架构

```
AI服务器 → MQTT/WebSocket → AiChatService
    ↓
opus音频数据 → OpusMediaPlayer
    ↓
OpusJniHandler解码 → PCM数据 → 临时WAV文件
    ↓
MediaPlayer播放 → 音频输出
    ↓
与LocalMediaPlayer智能协调
```

## 🎵 音频参数

- **编码器采样率**: 16kHz (发送到服务器)
- **解码器采样率**: 24kHz (接收解码)
- **声道数**: 1 (单声道)
- **帧大小**: 480样本 (24kHz × 20ms)
- **音频用途**: `USAGE_ASSISTANT` (语音助手)
- **内容类型**: `CONTENT_TYPE_SPEECH` (语音内容)

## 🔄 工作流程

### 1. 初始化阶段
```java
OpusMediaPlayer player = new OpusMediaPlayer(context);
player.setPlaybackListener(new PlaybackListener() {
    @Override
    public void onPlaybackStarted() {
        // AI开始说话
    }
    
    @Override
    public void onPlaybackCompleted() {
        // AI说话结束
    }
    
    @Override
    public void onPlaybackError(String error) {
        // 播放错误处理
    }
});
```

### 2. 音频数据处理
```java
// 接收opus数据
player.addOpusAudioData(opusBytes);

// 自动处理流程：
// 1. opus数据进入队列
// 2. 后台线程解码为PCM
// 3. 写入临时WAV文件
// 4. MediaPlayer播放
```

### 3. 播放控制
```java
player.stop();          // 停止播放
player.pause();         // 暂停播放
player.resume();        // 恢复播放
player.setVolume(0.5f); // 设置音量
player.release();       // 释放资源
```

## 🎛️ 在AiChatService中的集成

### 初始化
```java
// 在AiChatService.initializeComponents()中
opusMediaPlayer = new OpusMediaPlayer(this);
opusMediaPlayer.setPlaybackListener(new OpusMediaPlayer.PlaybackListener() {
    @Override
    public void onPlaybackStarted() {
        // 确保AI说话状态正确
        if (!isAiSpeaking.get()) {
            startAiSpeaking();
        }
    }
    
    @Override
    public void onPlaybackCompleted() {
        // AI说话结束
        stopAiSpeaking();
    }
    
    @Override
    public void onPlaybackError(String error) {
        Log.e(TAG, "AI语音播放错误: " + error);
        stopAiSpeaking();
    }
});
```

### 音频数据处理
```java
@Override
public void onAudioReceived(byte[] audioData, int sampleRate) {
    // 收到opus编码的音频数据，直接发送给OpusMediaPlayer处理
    if (audioData != null && audioData.length > 0) {
        Log.d(TAG, "收到AI语音数据: " + audioData.length + " bytes");
        opusMediaPlayer.addOpusAudioData(audioData);
    }
}
```

## 🔧 与LocalMediaPlayer的协调

### 智能音量管理
```java
// AI开始说话时
private void startAiSpeaking() {
    isAiSpeaking.set(true);
    
    // 通知LocalMediaPlayer降低音量
    if (mediaPlayer != null) {
        Method method = mediaPlayer.getMethods().getMethod("onAiSpeakStart");
        if (method != null) {
            method.invoke(new Parameter.ParameterList());
        }
    }
}

// AI结束说话时
private void stopAiSpeaking() {
    isAiSpeaking.set(false);
    
    // 通知LocalMediaPlayer恢复音量
    if (mediaPlayer != null) {
        Method method = mediaPlayer.getMethods().getMethod("onAiSpeakEnd");
        if (method != null) {
            method.invoke(new Parameter.ParameterList());
        }
    }
}
```

## 📁 文件结构

```
OpusMediaPlayer.java
├── 音频队列管理 (BlockingQueue<byte[]>)
├── OpusJniHandler集成 (opus解码)
├── MediaPlayer管理 (播放控制)
├── 临时文件管理 (WAV文件生成)
└── 播放状态管理 (监听器通知)
```

## 🎯 优势对比

### vs AudioManager播放
| 特性 | OpusMediaPlayer | AudioManager |
|------|----------------|--------------|
| 音频回声处理 | ✅ 更好 | ❌ 一般 |
| 音质 | ✅ 更高 | ❌ 较低 |
| 系统集成 | ✅ 更好 | ❌ 基础 |
| 音频焦点 | ✅ 自动管理 | ❌ 需手动 |
| 播放控制 | ✅ 丰富 | ❌ 基础 |

## 🔍 调试信息

### 日志标签
- `OpusMediaPlayer`: 主要播放逻辑
- `AiChatService`: 服务集成
- `OpusJniHandler`: opus解码

### 关键日志
```
OpusMediaPlayer: 添加opus音频数据: 1024 bytes
OpusMediaPlayer: Opus解码成功: 1024 bytes -> 1920 bytes PCM
OpusMediaPlayer: 开始准备播放AI语音，数据大小: 1920 bytes
OpusMediaPlayer: AI语音准备完成，开始播放
OpusMediaPlayer: AI语音播放完成
```

## ⚠️ 注意事项

1. **临时文件管理**: 自动创建和清理临时WAV文件
2. **内存管理**: 及时释放OpusJniHandler资源
3. **线程安全**: 音频处理在后台线程，UI回调在主线程
4. **错误处理**: 完善的异常捕获和恢复机制
5. **音频格式**: 确保WAV文件头格式正确

## 🚀 使用效果

通过使用OpusMediaPlayer，AI语音播放将获得：
- 🎵 **更好的音质** - 24kHz采样率
- 🔇 **更好的回声处理** - MediaPlayer优势
- 🎛️ **智能音量管理** - 与本地媒体协调
- 📱 **更好的系统集成** - 语音助手音频属性
- 🔄 **流畅的播放体验** - 实时流式播放

这样的设计让小萝卜机器人的AI语音交互更加自然和高质量！🤖✨
