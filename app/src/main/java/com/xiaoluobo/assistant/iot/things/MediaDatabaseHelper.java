package com.xiaoluobo.assistant.utils;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 媒体数据库管理类
 * 用于管理本地媒体文件的元数据和分类信息
 */
public class MediaDatabaseHelper extends SQLiteOpenHelper {
    
    private static final String TAG = "MediaDatabaseHelper";
    private static final String DATABASE_NAME = "media_library.db";
    private static final int DATABASE_VERSION = 1;
    
    // 媒体表
    private static final String TABLE_MEDIA = "media";
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_FILE_NAME = "file_name";
    private static final String COLUMN_DISPLAY_NAME = "display_name";
    private static final String COLUMN_CATEGORY = "category";
    private static final String COLUMN_SUB_CATEGORY = "sub_category";
    private static final String COLUMN_FILE_PATH = "file_path";
    private static final String COLUMN_DURATION = "duration";
    private static final String COLUMN_TAGS = "tags";
    private static final String COLUMN_DESCRIPTION = "description";
    private static final String COLUMN_AGE_GROUP = "age_group";
    private static final String COLUMN_CREATED_TIME = "created_time";
    
    // 分类表
    private static final String TABLE_CATEGORIES = "categories";
    private static final String COLUMN_CATEGORY_ID = "category_id";
    private static final String COLUMN_CATEGORY_NAME = "category_name";
    private static final String COLUMN_CATEGORY_TYPE = "category_type"; // story, song, education
    private static final String COLUMN_CATEGORY_DESC = "category_desc";
    private static final String COLUMN_MEDIA_COUNT = "media_count";
    
    public MediaDatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        // 创建媒体表
        String createMediaTable = "CREATE TABLE " + TABLE_MEDIA + " (" +
                COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                COLUMN_FILE_NAME + " TEXT NOT NULL, " +
                COLUMN_DISPLAY_NAME + " TEXT NOT NULL, " +
                COLUMN_CATEGORY + " TEXT NOT NULL, " +
                COLUMN_SUB_CATEGORY + " TEXT, " +
                COLUMN_FILE_PATH + " TEXT NOT NULL, " +
                COLUMN_DURATION + " INTEGER DEFAULT 0, " +
                COLUMN_TAGS + " TEXT, " +
                COLUMN_DESCRIPTION + " TEXT, " +
                COLUMN_AGE_GROUP + " TEXT, " +
                COLUMN_CREATED_TIME + " INTEGER DEFAULT 0" +
                ")";
        
        // 创建分类表
        String createCategoriesTable = "CREATE TABLE " + TABLE_CATEGORIES + " (" +
                COLUMN_CATEGORY_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                COLUMN_CATEGORY_NAME + " TEXT NOT NULL UNIQUE, " +
                COLUMN_CATEGORY_TYPE + " TEXT NOT NULL, " +
                COLUMN_CATEGORY_DESC + " TEXT, " +
                COLUMN_MEDIA_COUNT + " INTEGER DEFAULT 0" +
                ")";
        
        db.execSQL(createMediaTable);
        db.execSQL(createCategoriesTable);
        
        // 创建索引
        db.execSQL("CREATE INDEX idx_media_category ON " + TABLE_MEDIA + "(" + COLUMN_CATEGORY + ")");
        db.execSQL("CREATE INDEX idx_media_display_name ON " + TABLE_MEDIA + "(" + COLUMN_DISPLAY_NAME + ")");
        db.execSQL("CREATE INDEX idx_media_tags ON " + TABLE_MEDIA + "(" + COLUMN_TAGS + ")");
        
        Log.i(TAG, "数据库创建完成");
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_MEDIA);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_CATEGORIES);
        onCreate(db);
    }
    
    /**
     * 媒体项数据类
     */
    public static class MediaItem {
        public long id;
        public String fileName;
        public String displayName;
        public String category;
        public String subCategory;
        public String filePath;
        public long duration;
        public String tags;
        public String description;
        public String ageGroup;
        public long createdTime;
        
        public MediaItem() {}
        
        public MediaItem(String fileName, String displayName, String category, String filePath) {
            this.fileName = fileName;
            this.displayName = displayName;
            this.category = category;
            this.filePath = filePath;
            this.createdTime = System.currentTimeMillis();
        }
    }
    
    /**
     * 分类数据类
     */
    public static class CategoryInfo {
        public long id;
        public String name;
        public String type;
        public String description;
        public int mediaCount;
        
        public CategoryInfo() {}
        
        public CategoryInfo(String name, String type, String description) {
            this.name = name;
            this.type = type;
            this.description = description;
        }
    }
    
    /**
     * 插入媒体项
     */
    public long insertMediaItem(MediaItem item) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_FILE_NAME, item.fileName);
        values.put(COLUMN_DISPLAY_NAME, item.displayName);
        values.put(COLUMN_CATEGORY, item.category);
        values.put(COLUMN_SUB_CATEGORY, item.subCategory);
        values.put(COLUMN_FILE_PATH, item.filePath);
        values.put(COLUMN_DURATION, item.duration);
        values.put(COLUMN_TAGS, item.tags);
        values.put(COLUMN_DESCRIPTION, item.description);
        values.put(COLUMN_AGE_GROUP, item.ageGroup);
        values.put(COLUMN_CREATED_TIME, item.createdTime);
        
        long id = db.insert(TABLE_MEDIA, null, values);
        db.close();
        
        // 更新分类计数
        updateCategoryCount(item.category);
        
        return id;
    }
    
    /**
     * 插入或更新分类信息
     */
    public long insertOrUpdateCategory(CategoryInfo category) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_CATEGORY_NAME, category.name);
        values.put(COLUMN_CATEGORY_TYPE, category.type);
        values.put(COLUMN_CATEGORY_DESC, category.description);
        
        long id = db.insertWithOnConflict(TABLE_CATEGORIES, null, values, SQLiteDatabase.CONFLICT_REPLACE);
        db.close();
        
        return id;
    }
    
    /**
     * 更新分类媒体计数
     */
    private void updateCategoryCount(String categoryName) {
        SQLiteDatabase db = this.getWritableDatabase();
        
        // 计算该分类的媒体数量
        String countQuery = "SELECT COUNT(*) FROM " + TABLE_MEDIA + " WHERE " + COLUMN_CATEGORY + " = ?";
        Cursor cursor = db.rawQuery(countQuery, new String[]{categoryName});
        
        int count = 0;
        if (cursor.moveToFirst()) {
            count = cursor.getInt(0);
        }
        cursor.close();
        
        // 更新分类表中的计数
        ContentValues values = new ContentValues();
        values.put(COLUMN_MEDIA_COUNT, count);
        
        db.update(TABLE_CATEGORIES, values, COLUMN_CATEGORY_NAME + " = ?", new String[]{categoryName});
        db.close();
    }
    
    /**
     * 根据关键词搜索媒体
     */
    public List<MediaItem> searchMedia(String keyword) {
        List<MediaItem> results = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        String query = "SELECT * FROM " + TABLE_MEDIA + 
                      " WHERE " + COLUMN_DISPLAY_NAME + " LIKE ? OR " +
                      COLUMN_TAGS + " LIKE ? OR " +
                      COLUMN_DESCRIPTION + " LIKE ? " +
                      " ORDER BY " + COLUMN_DISPLAY_NAME;
        
        String searchPattern = "%" + keyword + "%";
        Cursor cursor = db.rawQuery(query, new String[]{searchPattern, searchPattern, searchPattern});
        
        if (cursor.moveToFirst()) {
            do {
                MediaItem item = cursorToMediaItem(cursor);
                results.add(item);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        db.close();
        
        return results;
    }
    
    /**
     * 获取指定分类的媒体列表
     */
    public List<MediaItem> getMediaByCategory(String category) {
        List<MediaItem> results = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        String query = "SELECT * FROM " + TABLE_MEDIA + 
                      " WHERE " + COLUMN_CATEGORY + " = ? " +
                      " ORDER BY " + COLUMN_DISPLAY_NAME;
        
        Cursor cursor = db.rawQuery(query, new String[]{category});
        
        if (cursor.moveToFirst()) {
            do {
                MediaItem item = cursorToMediaItem(cursor);
                results.add(item);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        db.close();
        
        return results;
    }
    
    /**
     * 获取所有分类信息
     */
    public List<CategoryInfo> getAllCategories() {
        List<CategoryInfo> categories = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        String query = "SELECT * FROM " + TABLE_CATEGORIES + " ORDER BY " + COLUMN_CATEGORY_TYPE + ", " + COLUMN_CATEGORY_NAME;
        Cursor cursor = db.rawQuery(query, null);
        
        if (cursor.moveToFirst()) {
            do {
                CategoryInfo category = cursorToCategoryInfo(cursor);
                categories.add(category);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        db.close();
        
        return categories;
    }
    
    /**
     * 获取分类统计信息
     */
    public Map<String, Integer> getCategoryStats() {
        Map<String, Integer> stats = new HashMap<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        String query = "SELECT " + COLUMN_CATEGORY + ", COUNT(*) as count FROM " + TABLE_MEDIA + 
                      " GROUP BY " + COLUMN_CATEGORY;
        
        Cursor cursor = db.rawQuery(query, null);
        
        if (cursor.moveToFirst()) {
            do {
                String category = cursor.getString(0);
                int count = cursor.getInt(1);
                stats.put(category, count);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        db.close();
        
        return stats;
    }
    
    /**
     * 清空所有数据
     */
    public void clearAllData() {
        SQLiteDatabase db = this.getWritableDatabase();
        db.delete(TABLE_MEDIA, null, null);
        db.delete(TABLE_CATEGORIES, null, null);
        db.close();
        Log.i(TAG, "所有数据已清空");
    }
    
    /**
     * 将Cursor转换为MediaItem
     */
    private MediaItem cursorToMediaItem(Cursor cursor) {
        MediaItem item = new MediaItem();
        item.id = cursor.getLong(cursor.getColumnIndex(COLUMN_ID));
        item.fileName = cursor.getString(cursor.getColumnIndex(COLUMN_FILE_NAME));
        item.displayName = cursor.getString(cursor.getColumnIndex(COLUMN_DISPLAY_NAME));
        item.category = cursor.getString(cursor.getColumnIndex(COLUMN_CATEGORY));
        item.subCategory = cursor.getString(cursor.getColumnIndex(COLUMN_SUB_CATEGORY));
        item.filePath = cursor.getString(cursor.getColumnIndex(COLUMN_FILE_PATH));
        item.duration = cursor.getLong(cursor.getColumnIndex(COLUMN_DURATION));
        item.tags = cursor.getString(cursor.getColumnIndex(COLUMN_TAGS));
        item.description = cursor.getString(cursor.getColumnIndex(COLUMN_DESCRIPTION));
        item.ageGroup = cursor.getString(cursor.getColumnIndex(COLUMN_AGE_GROUP));
        item.createdTime = cursor.getLong(cursor.getColumnIndex(COLUMN_CREATED_TIME));
        return item;
    }
    
    /**
     * 将Cursor转换为CategoryInfo
     */
    private CategoryInfo cursorToCategoryInfo(Cursor cursor) {
        CategoryInfo category = new CategoryInfo();
        category.id = cursor.getLong(cursor.getColumnIndex(COLUMN_CATEGORY_ID));
        category.name = cursor.getString(cursor.getColumnIndex(COLUMN_CATEGORY_NAME));
        category.type = cursor.getString(cursor.getColumnIndex(COLUMN_CATEGORY_TYPE));
        category.description = cursor.getString(cursor.getColumnIndex(COLUMN_CATEGORY_DESC));
        category.mediaCount = cursor.getInt(cursor.getColumnIndex(COLUMN_MEDIA_COUNT));
        return category;
    }
}
