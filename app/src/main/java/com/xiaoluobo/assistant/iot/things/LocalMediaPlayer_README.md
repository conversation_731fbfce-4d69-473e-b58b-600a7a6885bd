# LocalMediaPlayer - 本地媒体播放器Thing

## 概述

LocalMediaPlayer是一个基于IoT框架的本地媒体播放器设备，专门用于播放存储在设备本地的音频资源，如儿童故事、儿歌等。

## 功能特性

### 1. 媒体库管理
- **自动扫描**: 自动扫描指定目录下的音频文件
- **分类管理**: 根据文件夹结构自动分类媒体文件
- **支持格式**: MP3, WAV, M4A, AAC, OGG, FLAC
- **媒体信息**: 自动提取文件名和显示名称

### 2. 播放控制
- **基本控制**: 播放、暂停、停止、恢复
- **列表控制**: 上一首、下一首
- **音频焦点**: 自动管理音频焦点，避免与其他应用冲突

### 3. 播放模式
- **顺序播放**: 按列表顺序播放
- **随机播放**: 随机选择播放
- **单曲循环**: 重复播放当前歌曲
- **列表循环**: 播放完列表后重新开始

### 4. 播放列表管理
- **分类播放**: 播放指定分类的所有媒体
- **搜索播放**: 根据关键词搜索并播放
- **自定义列表**: 支持创建自定义播放列表

## 媒体文件组织结构

推荐的媒体文件目录结构：

```
media/
├── 儿童故事《夏风入梦精选1047篇》/
│   ├── 0001集 不肯长大的小泰莱莎.mp3
│   ├── 0002集 胡桃夹子.mp3
│   └── ...
├── 宝宝巴士儿歌大全586首/
│   ├── 1.【经典儿歌】懂礼貌.mp3
│   ├── 2.【经典儿歌】我爱洗澡.mp3
│   └── ...
└── 儿童睡前故事《讲给孩子的三十六计》/
    ├── 01 三十六计 第一计 瞒天过海.mp3
    ├── 02 三十六计 第二计 围魏救赵.mp3
    └── ...
```

## 设备属性

### 播放状态属性
- `isPlaying`: 当前播放状态 (boolean)
- `isPaused`: 当前暂停状态 (boolean)
- `isBuffering`: 当前缓冲状态 (boolean)

### 当前播放信息属性
- `currentMedia`: 当前播放媒体名称 (string)
- `currentCategory`: 当前播放分类 (string)
- `playMode`: 播放模式 (string)

### 播放列表信息属性
- `playlistSize`: 播放列表大小 (number)
- `currentIndex`: 当前播放索引 (number)

### 媒体库信息属性
- `totalMediaCount`: 媒体库总数 (number)
- `categoryCount`: 分类数量 (number)
- `categories`: 媒体分类列表 (string)

### 分类属性
- `category_[分类名]`: 每个分类的媒体列表 (string)

### 错误状态属性
- `errorStatus`: 播放错误状态 (string)

## 设备方法

### 播放控制方法
- `play()`: 开始播放
- `pause()`: 暂停播放
- `stop()`: 停止播放
- `resume()`: 恢复播放
- `next()`: 播放下一首
- `previous()`: 播放上一首

### 播放列表管理方法
- `playCategory(category)`: 播放指定分类
  - 参数: `category` (string) - 分类名称
- `playMedia(mediaName)`: 播放指定媒体
  - 参数: `mediaName` (string) - 媒体名称
- `playAllRandom()`: 随机播放所有媒体
- `clearPlaylist()`: 清空播放列表

### 播放模式控制方法
- `setPlayMode(mode)`: 设置播放模式
  - 参数: `mode` (string) - 播放模式
  - 可选值: `sequential`, `random`, `repeat_one`, `repeat_all`

### 媒体搜索和管理方法
- `searchMedia(keyword)`: 搜索媒体
  - 参数: `keyword` (string) - 搜索关键词
- `refreshMediaLibrary()`: 刷新媒体库

## 使用示例

### 1. 播放指定分类的媒体
```json
{
  "method": "playCategory",
  "parameters": {
    "category": "儿童故事《夏风入梦精选1047篇》"
  }
}
```

### 2. 搜索并播放媒体
```json
{
  "method": "searchMedia",
  "parameters": {
    "keyword": "小红帽"
  }
}
```

### 3. 设置播放模式为随机播放
```json
{
  "method": "setPlayMode",
  "parameters": {
    "mode": "random"
  }
}
```

### 4. 播放指定媒体
```json
{
  "method": "playMedia",
  "parameters": {
    "mediaName": "胡桃夹子"
  }
}
```

## 媒体文件部署

### 方法1: 使用外部存储
1. 将媒体文件复制到设备的外部存储目录
2. 路径通常为: `/storage/emulated/0/media/`

### 方法2: 使用应用外部文件目录
1. 将媒体文件复制到应用的外部文件目录
2. 路径通常为: `/storage/emulated/0/Android/data/[包名]/files/../media/`

### 方法3: 使用SD卡
1. 将媒体文件复制到SD卡的media目录
2. 路径通常为: `/storage/[SD卡ID]/media/`

## 注意事项

1. **权限要求**: 需要READ_EXTERNAL_STORAGE权限来访问外部存储的媒体文件
2. **音频焦点**: 播放时会自动请求音频焦点，停止时会释放
3. **文件格式**: 支持常见的音频格式，推荐使用MP3格式
4. **文件命名**: 建议使用有意义的文件名，系统会自动提取显示名称
5. **目录结构**: 建议按分类组织文件夹，便于管理和播放

## 故障排除

### 1. 媒体文件无法播放
- 检查文件路径是否正确
- 确认文件格式是否支持
- 检查文件是否损坏

### 2. 无法扫描到媒体文件
- 检查媒体目录是否存在
- 确认应用是否有读取权限
- 尝试刷新媒体库

### 3. 播放中断或无声音
- 检查音频焦点是否被其他应用占用
- 确认设备音量设置
- 检查音频输出设备

## 扩展功能

LocalMediaPlayer设计为可扩展的架构，未来可以添加以下功能：
- 播放历史记录
- 收藏夹管理
- 播放进度控制
- 音效处理
- 网络媒体支持
- 播放统计
