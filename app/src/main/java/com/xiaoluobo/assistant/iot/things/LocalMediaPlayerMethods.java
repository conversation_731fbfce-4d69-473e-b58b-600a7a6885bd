package com.xiaoluobo.assistant.iot.things;

import android.util.Log;
import com.xiaoluobo.assistant.utils.MediaDatabaseHelper;
import java.util.List;
import java.util.ArrayList;
import java.util.Random;

/**
 * LocalMediaPlayer的方法实现类
 * 包含所有播放控制和智能搜索方法的具体实现
 */
public class LocalMediaPlayerMethods {
    
    private static final String TAG = "LocalMediaPlayerMethods";
    
    /**
     * 重新播放当前故事
     */
    public static void replayCurrentStory(LocalMediaPlayer player) {
        if (player.currentMediaItem != null) {
            Log.i(TAG, "重新播放当前故事: " + player.currentMediaItem.displayName);
            player.playCurrentItem();
        } else {
            Log.w(TAG, "没有当前播放的故事可以重播");
        }
    }
    
    /**
     * 根据分类类型随机播放
     */
    public static void playRandomFromCategory(LocalMediaPlayer player, String categoryType) {
        List<MediaDatabaseHelper.MediaItem> matchingItems = new ArrayList<>();
        
        // 从数据库获取匹配类型的所有媒体
        List<MediaDatabaseHelper.CategoryInfo> categories = player.dbHelper.getAllCategories();
        
        for (MediaDatabaseHelper.CategoryInfo category : categories) {
            if (category.type.equals(categoryType)) {
                List<MediaDatabaseHelper.MediaItem> items = player.dbHelper.getMediaByCategory(category.name);
                matchingItems.addAll(items);
            }
        }
        
        if (matchingItems.isEmpty()) {
            Log.w(TAG, "没有找到类型为 " + categoryType + " 的媒体");
            player.lastErrorMessage = "没有找到" + (categoryType.equals("story") ? "故事" : "儿歌");
            return;
        }
        
        // 随机选择一个
        Random random = new Random();
        MediaDatabaseHelper.MediaItem randomItem = matchingItems.get(random.nextInt(matchingItems.size()));
        
        // 设置播放列表为单个文件
        player.currentPlaylist.clear();
        player.currentPlaylist.add(randomItem);
        player.currentIndex = 0;
        
        Log.i(TAG, "随机播放" + categoryType + ": " + randomItem.displayName);
        player.playCurrentItem();
    }
    
    /**
     * 搜索并播放
     */
    public static void searchAndPlay(LocalMediaPlayer player, String keyword) {
        List<MediaDatabaseHelper.MediaItem> searchResults = player.dbHelper.searchMedia(keyword);
        
        if (searchResults.isEmpty()) {
            Log.w(TAG, "搜索关键词 '" + keyword + "' 没有找到结果");
            player.lastErrorMessage = "没有找到包含 '" + keyword + "' 的内容";
            return;
        }
        
        // 设置播放列表为搜索结果
        player.currentPlaylist.clear();
        player.currentPlaylist.addAll(searchResults);
        player.currentIndex = 0;
        
        Log.i(TAG, "搜索到 " + searchResults.size() + " 个结果，开始播放: " + searchResults.get(0).displayName);
        player.playCurrentItem();
    }
    
    /**
     * 智能搜索并播放
     */
    public static void smartSearchAndPlay(LocalMediaPlayer player, String content) {
        Log.i(TAG, "智能搜索内容: " + content);
        
        // 智能关键词映射
        String searchKeyword = mapContentToKeyword(content);
        
        if (searchKeyword != null) {
            searchAndPlay(player, searchKeyword);
        } else {
            // 直接使用原始内容搜索
            searchAndPlay(player, content);
        }
    }
    
    /**
     * 将用户输入的内容映射到搜索关键词
     */
    private static String mapContentToKeyword(String content) {
        content = content.toLowerCase();
        
        // 经典童话映射
        if (content.contains("小红帽") || content.contains("红帽")) {
            return "小红帽";
        } else if (content.contains("白雪公主") || content.contains("白雪")) {
            return "白雪公主";
        } else if (content.contains("灰姑娘")) {
            return "灰姑娘";
        } else if (content.contains("三只小猪") || content.contains("小猪")) {
            return "三只小猪";
        } else if (content.contains("龟兔赛跑") || content.contains("乌龟") || content.contains("兔子赛跑")) {
            return "龟兔赛跑";
        } else if (content.contains("胡桃夹子")) {
            return "胡桃夹子";
        }
        
        // 教育主题映射
        else if (content.contains("安全") || content.contains("保护自己")) {
            return "安全";
        } else if (content.contains("习惯") || content.contains("好习惯")) {
            return "习惯";
        } else if (content.contains("礼貌") || content.contains("懂礼貌")) {
            return "礼貌";
        }
        
        // 动物主题映射
        else if (content.contains("动物") || content.contains("小动物")) {
            return "动物";
        } else if (content.contains("恐龙")) {
            return "恐龙";
        }
        
        // 数字和颜色映射
        else if (content.contains("数字") || content.contains("数数")) {
            return "数字";
        } else if (content.contains("颜色")) {
            return "颜色";
        }
        
        // 职业体验映射
        else if (content.contains("职业") || content.contains("工作")) {
            return "职业";
        }
        
        // 古诗词映射
        else if (content.contains("古诗") || content.contains("诗词")) {
            return "古诗";
        }
        
        return null; // 没有找到映射，返回null使用原始内容搜索
    }
    
    /**
     * 按年龄组播放内容
     */
    public static void playForAgeGroup(LocalMediaPlayer player, String ageInput) {
        String ageGroup = mapAgeToGroup(ageInput);
        
        if (ageGroup == null) {
            Log.w(TAG, "无法识别年龄: " + ageInput);
            player.lastErrorMessage = "无法识别年龄: " + ageInput;
            return;
        }
        
        // 搜索适合该年龄组的内容
        // 这里可以根据数据库中的ageGroup字段进行搜索
        // 暂时使用分类来近似匹配
        if (ageGroup.equals("2-6岁")) {
            player.playCategory("宝宝巴士儿歌大全586首");
        } else if (ageGroup.equals("6-12岁")) {
            player.playCategory("儿童睡前故事《讲给孩子的三十六计》");
        } else {
            player.playCategory("儿童故事《夏风入梦精选1047篇》");
        }
    }
    
    /**
     * 将年龄输入映射到年龄组
     */
    private static String mapAgeToGroup(String ageInput) {
        try {
            // 尝试解析数字
            int age = Integer.parseInt(ageInput.replaceAll("[^0-9]", ""));
            
            if (age >= 2 && age <= 6) {
                return "2-6岁";
            } else if (age >= 6 && age <= 12) {
                return "6-12岁";
            } else if (age >= 3 && age <= 8) {
                return "3-8岁";
            }
        } catch (NumberFormatException e) {
            // 无法解析数字，尝试文本匹配
            if (ageInput.contains("宝宝") || ageInput.contains("小朋友")) {
                return "2-6岁";
            } else if (ageInput.contains("大一点") || ageInput.contains("大孩子")) {
                return "6-12岁";
            }
        }
        
        return "3-8岁"; // 默认年龄组
    }
    
    /**
     * 按主题播放内容
     */
    public static void playByTheme(LocalMediaPlayer player, String theme) {
        Log.i(TAG, "按主题播放: " + theme);
        
        // 主题关键词映射
        String keyword = mapThemeToKeyword(theme);
        
        if (keyword != null) {
            searchAndPlay(player, keyword);
        } else {
            // 直接使用主题作为关键词搜索
            searchAndPlay(player, theme);
        }
    }
    
    /**
     * 将主题映射到关键词
     */
    private static String mapThemeToKeyword(String theme) {
        theme = theme.toLowerCase();
        
        if (theme.contains("睡前") || theme.contains("睡觉")) {
            return "睡前";
        } else if (theme.contains("教育") || theme.contains("学习")) {
            return "教育";
        } else if (theme.contains("安全")) {
            return "安全";
        } else if (theme.contains("习惯")) {
            return "习惯";
        } else if (theme.contains("动物")) {
            return "动物";
        } else if (theme.contains("童话")) {
            return "童话";
        } else if (theme.contains("儿歌") || theme.contains("歌曲")) {
            return "儿歌";
        }
        
        return null;
    }
    
    /**
     * 获取推荐内容
     */
    public static void getRecommendations(LocalMediaPlayer player) {
        Log.i(TAG, "获取推荐内容");
        
        // 简单的推荐逻辑：随机推荐一些热门内容
        String[] recommendations = {
            "小红帽", "白雪公主", "三只小猪", "龟兔赛跑", 
            "懂礼貌", "我爱洗澡", "安全歌", "数字歌"
        };
        
        Random random = new Random();
        String recommendation = recommendations[random.nextInt(recommendations.length)];
        
        Log.i(TAG, "推荐内容: " + recommendation);
        searchAndPlay(player, recommendation);
    }
}
