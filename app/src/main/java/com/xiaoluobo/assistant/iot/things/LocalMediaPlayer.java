package com.xiaoluobo.assistant.iot.things;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.xiaoluobo.assistant.AssistantApplication;
import com.xiaoluobo.assistant.iot.Parameter;
import com.xiaoluobo.assistant.iot.Thing;
import com.xiaoluobo.assistant.iot.ValueType;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 本地媒体播放器设备
 * 通过IoT框架控制本地音频资源播放
 */
public class LocalMediaPlayer extends Thing {

    private static final String TAG = "Thing.LocalMediaPlayer";
    
    // 媒体文件根目录
    private static final String MEDIA_ROOT_PATH = "/android_asset/media";
    private File mediaRootDir;
    
    // 媒体资源分类
    private final Map<String, List<MediaItem>> mediaCategories = new HashMap<>();
    
    // 所有媒体文件列表
    private final List<MediaItem> allMediaItems = new ArrayList<>();
    
    // 当前播放列表
    private List<MediaItem> currentPlaylist = new ArrayList<>();
    
    // 当前播放索引
    private int currentIndex = -1;
    
    // 媒体播放器
    private MediaPlayer mediaPlayer;
    
    // 播放状态
    private boolean isPlaying = false;
    private boolean isPaused = false;
    private boolean isBuffering = false;
    
    // 播放模式
    public enum PlayMode {
        SEQUENTIAL,  // 顺序播放
        RANDOM,      // 随机播放
        REPEAT_ONE,  // 单曲循环
        REPEAT_ALL   // 列表循环
    }
    
    private PlayMode currentPlayMode = PlayMode.SEQUENTIAL;
    
    // 当前播放的媒体项
    private MediaItem currentMediaItem;
    
    // 错误信息
    private String lastErrorMessage = "";
    
    // 处理器，用于延迟操作
    private final Handler handler = new Handler(Looper.getMainLooper());
    
    // 音频焦点辅助类
    private AudioFocusHelper audioFocusHelper;
    
    /**
     * 媒体项数据类
     */
    public static class MediaItem {
        private String fileName;
        private String displayName;
        private String category;
        private String filePath;
        private long duration = -1; // 时长（毫秒）
        
        public MediaItem(String fileName, String displayName, String category, String filePath) {
            this.fileName = fileName;
            this.displayName = displayName;
            this.category = category;
            this.filePath = filePath;
        }
        
        // Getters
        public String getFileName() { return fileName; }
        public String getDisplayName() { return displayName; }
        public String getCategory() { return category; }
        public String getFilePath() { return filePath; }
        public long getDuration() { return duration; }
        
        // Setters
        public void setDuration(long duration) { this.duration = duration; }
        
        @Override
        public String toString() {
            return displayName + " (" + category + ")";
        }
    }
    
    /**
     * 构造函数
     */
    public LocalMediaPlayer() {
        super("LocalMediaPlayer", "本地媒体播放器");
        
        // 初始化媒体根目录
        initMediaRootDirectory();
        
        // 初始化音频焦点辅助类
        audioFocusHelper = new AudioFocusHelper(AssistantApplication.getContext());
        
        // 扫描媒体文件
        scanMediaFiles();
        
        // 初始化媒体播放器
        initMediaPlayer();
        
        // 初始化设备属性
        initProperties();
        
        // 初始化设备方法
        initMethods();
    }
    
    /**
     * 初始化媒体根目录
     */
    private void initMediaRootDirectory() {
        Context context = AssistantApplication.getContext();
        // 使用外部存储的media目录
        File externalDir = context.getExternalFilesDir(null);
        if (externalDir != null) {
            mediaRootDir = new File(externalDir.getParent(), "media");
        } else {
            // 备用方案：使用内部存储
            mediaRootDir = new File(context.getFilesDir(), "media");
        }
        
        // 如果目录不存在，尝试使用项目根目录下的media文件夹
        if (!mediaRootDir.exists()) {
            // 尝试使用项目根目录的media文件夹
            String projectRoot = System.getProperty("user.dir");
            if (projectRoot != null) {
                mediaRootDir = new File(projectRoot, "media");
            }
        }
        
        Log.i(TAG, "媒体根目录: " + mediaRootDir.getAbsolutePath());
        Log.i(TAG, "媒体根目录存在: " + mediaRootDir.exists());
    }
    
    /**
     * 扫描媒体文件
     */
    private void scanMediaFiles() {
        if (!mediaRootDir.exists()) {
            Log.w(TAG, "媒体根目录不存在，无法扫描媒体文件");
            return;
        }
        
        Log.i(TAG, "开始扫描媒体文件...");
        scanDirectory(mediaRootDir, "");
        
        Log.i(TAG, "媒体文件扫描完成，共找到 " + allMediaItems.size() + " 个文件");
        Log.i(TAG, "媒体分类数量: " + mediaCategories.size());
        
        for (String category : mediaCategories.keySet()) {
            Log.i(TAG, "分类 [" + category + "] 包含 " + mediaCategories.get(category).size() + " 个文件");
        }
    }
    
    /**
     * 递归扫描目录
     */
    private void scanDirectory(File directory, String categoryPrefix) {
        File[] files = directory.listFiles();
        if (files == null) return;
        
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归扫描子目录
                String subCategory = categoryPrefix.isEmpty() ? file.getName() : categoryPrefix + "/" + file.getName();
                scanDirectory(file, subCategory);
            } else if (isAudioFile(file)) {
                // 处理音频文件
                String category = categoryPrefix.isEmpty() ? "未分类" : categoryPrefix;
                String displayName = getDisplayName(file.getName());
                
                MediaItem item = new MediaItem(file.getName(), displayName, category, file.getAbsolutePath());
                allMediaItems.add(item);
                
                // 添加到分类
                if (!mediaCategories.containsKey(category)) {
                    mediaCategories.put(category, new ArrayList<>());
                }
                mediaCategories.get(category).add(item);
            }
        }
    }
    
    /**
     * 判断是否为音频文件
     */
    private boolean isAudioFile(File file) {
        String name = file.getName().toLowerCase();
        return name.endsWith(".mp3") || name.endsWith(".wav") || 
               name.endsWith(".m4a") || name.endsWith(".aac") || 
               name.endsWith(".ogg") || name.endsWith(".flac");
    }
    
    /**
     * 从文件名获取显示名称
     */
    private String getDisplayName(String fileName) {
        // 移除文件扩展名
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            fileName = fileName.substring(0, lastDot);
        }
        
        // 移除编号前缀（如"0001集 "）
        fileName = fileName.replaceFirst("^\\d+集\\s*", "");
        
        return fileName;
    }
    
    /**
     * 初始化媒体播放器
     */
    private void initMediaPlayer() {
        mediaPlayer = new MediaPlayer();
        
        // 使用AudioAttributes代替过时的setAudioStreamType
        AudioAttributes audioAttributes = new AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_MEDIA)
            .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
            .build();
        mediaPlayer.setAudioAttributes(audioAttributes);
        
        // 设置播放完成监听器
        mediaPlayer.setOnCompletionListener(mp -> {
            Log.i(TAG, "播放完成: " + (currentMediaItem != null ? currentMediaItem.getDisplayName() : "未知"));
            handlePlaybackCompletion();
        });
        
        // 设置错误监听器
        mediaPlayer.setOnErrorListener((mp, what, extra) -> {
            String errorMsg = "播放错误: what=" + what + ", extra=" + extra;
            Log.e(TAG, errorMsg);
            lastErrorMessage = errorMsg;
            
            isPlaying = false;
            isBuffering = false;
            isPaused = false;
            
            // 释放音频焦点
            audioFocusHelper.abandonAudioFocus();
            
            return false;
        });
        
        // 设置准备完成监听器
        mediaPlayer.setOnPreparedListener(mp -> {
            Log.i(TAG, "媒体准备完成，开始播放: " + (currentMediaItem != null ? currentMediaItem.getDisplayName() : "未知"));
            
            // 获取音频焦点后开始播放
            if (audioFocusHelper.requestAudioFocus()) {
                mp.start();
                isPlaying = true;
                isPaused = false;
                isBuffering = false;
                lastErrorMessage = "";
            } else {
                Log.e(TAG, "无法获取音频焦点，播放失败");
                isPlaying = false;
            }
        });
        
        // 设置缓冲更新监听器
        mediaPlayer.setOnBufferingUpdateListener((mp, percent) -> {
            isBuffering = (percent < 100);
        });
    }

    /**
     * 处理播放完成事件
     */
    private void handlePlaybackCompletion() {
        isPlaying = false;
        isPaused = false;
        isBuffering = false;

        // 释放音频焦点
        audioFocusHelper.abandonAudioFocus();

        // 根据播放模式决定下一步操作
        switch (currentPlayMode) {
            case REPEAT_ONE:
                // 单曲循环，重新播放当前歌曲
                playCurrentItem();
                break;
            case SEQUENTIAL:
            case REPEAT_ALL:
                // 顺序播放或列表循环，播放下一首
                playNext();
                break;
            case RANDOM:
                // 随机播放，随机选择下一首
                playRandomItem();
                break;
        }
    }

    /**
     * 初始化设备属性
     */
    private void initProperties() {
        // 播放状态属性
        properties.addBooleanProperty("isPlaying", "当前播放状态", () -> isPlaying);
        properties.addBooleanProperty("isPaused", "当前暂停状态", () -> isPaused);
        properties.addBooleanProperty("isBuffering", "当前缓冲状态", () -> isBuffering);

        // 当前播放信息属性
        properties.addStringProperty("currentMedia", "当前播放媒体", () ->
            currentMediaItem != null ? currentMediaItem.getDisplayName() : "无");
        properties.addStringProperty("currentCategory", "当前播放分类", () ->
            currentMediaItem != null ? currentMediaItem.getCategory() : "无");

        // 播放模式属性
        properties.addStringProperty("playMode", "播放模式", () -> getPlayModeString());

        // 播放列表信息属性
        properties.addNumberProperty("playlistSize", "播放列表大小", () -> currentPlaylist.size());
        properties.addNumberProperty("currentIndex", "当前播放索引", () -> currentIndex);

        // 媒体库信息属性
        properties.addNumberProperty("totalMediaCount", "媒体库总数", () -> allMediaItems.size());
        properties.addNumberProperty("categoryCount", "分类数量", () -> mediaCategories.size());

        // 分类列表属性
        properties.addStringProperty("categories", "媒体分类列表", this::getCategoriesString);

        // 为每个分类添加属性
        for (String category : mediaCategories.keySet()) {
            final String categoryName = category;
            properties.addStringProperty("category_" + categoryName.replaceAll("[^a-zA-Z0-9]", "_"),
                categoryName + "分类媒体列表", () -> getCategoryMediaString(categoryName));
        }

        // 错误信息属性
        properties.addStringProperty("errorStatus", "播放错误状态", () ->
            lastErrorMessage.isEmpty() ? "正常" : lastErrorMessage);
    }

    /**
     * 获取播放模式字符串
     */
    private String getPlayModeString() {
        switch (currentPlayMode) {
            case SEQUENTIAL: return "顺序播放";
            case RANDOM: return "随机播放";
            case REPEAT_ONE: return "单曲循环";
            case REPEAT_ALL: return "列表循环";
            default: return "未知";
        }
    }

    /**
     * 获取分类列表的字符串表示
     */
    private String getCategoriesString() {
        StringBuilder sb = new StringBuilder();
        sb.append("媒体分类（共 ").append(mediaCategories.size()).append(" 个分类）：\n\n");

        for (String category : mediaCategories.keySet()) {
            sb.append(category).append("（").append(mediaCategories.get(category).size()).append(" 个文件）\n");
        }
        return sb.toString();
    }

    /**
     * 获取指定分类媒体列表的字符串表示
     */
    private String getCategoryMediaString(String category) {
        if (!mediaCategories.containsKey(category)) {
            return "分类 '" + category + "' 不存在";
        }

        List<MediaItem> items = mediaCategories.get(category);
        if (items.isEmpty()) {
            return "分类 '" + category + "' 中没有媒体文件";
        }

        StringBuilder sb = new StringBuilder();
        sb.append(category).append("（").append(items.size()).append(" 个文件）：\n\n");

        for (int i = 0; i < Math.min(items.size(), 20); i++) { // 限制显示前20个
            MediaItem item = items.get(i);
            sb.append((i + 1)).append(". ").append(item.getDisplayName()).append("\n");
        }

        if (items.size() > 20) {
            sb.append("... 还有 ").append(items.size() - 20).append(" 个文件\n");
        }

        return sb.toString();
    }

    /**
     * 初始化设备方法
     */
    private void initMethods() {
        // 播放控制方法
        initPlaybackControlMethods();

        // 播放列表管理方法
        initPlaylistMethods();

        // 播放模式控制方法
        initPlayModeMethods();

        // 媒体搜索和选择方法
        initMediaSelectionMethods();
    }

    /**
     * 初始化播放控制方法
     */
    private void initPlaybackControlMethods() {
        // 播放方法
        methods.addMethod("play", "开始播放", new Parameter.ParameterList(), parameters -> {
            play();
        });

        // 暂停方法
        methods.addMethod("pause", "暂停播放", new Parameter.ParameterList(), parameters -> {
            pause();
        });

        // 停止方法
        methods.addMethod("stop", "停止播放", new Parameter.ParameterList(), parameters -> {
            stop();
        });

        // 恢复播放方法
        methods.addMethod("resume", "恢复播放", new Parameter.ParameterList(), parameters -> {
            resume();
        });

        // 下一首方法
        methods.addMethod("next", "播放下一首", new Parameter.ParameterList(), parameters -> {
            playNext();
        });

        // 上一首方法
        methods.addMethod("previous", "播放上一首", new Parameter.ParameterList(), parameters -> {
            playPrevious();
        });
    }
}
