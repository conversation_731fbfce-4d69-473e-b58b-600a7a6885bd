package com.xiaoluobo.assistant.iot.things;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.xiaoluobo.assistant.AssistantApplication;
import com.xiaoluobo.assistant.iot.Parameter;
import com.xiaoluobo.assistant.iot.Thing;
import com.xiaoluobo.assistant.iot.ValueType;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 本地媒体播放器设备 - 儿童故事和儿歌播放器
 * 通过IoT框架控制本地音频资源播放，专为儿童设计
 */
public class LocalMediaPlayer extends Thing {

    private static final String TAG = "Thing.LocalMediaPlayer";

    // 媒体文件根目录 - 使用SD卡路径
    private static final String MEDIA_ROOT_PATH = "/storage/sdcard0/Media";
    private File mediaRootDir;

    // 数据库管理器
    public MediaDatabaseHelper dbHelper;

    // 媒体资源分类缓存
    public final Map<String, List<MediaDatabaseHelper.MediaItem>> mediaCategories = new HashMap<>();

    // 当前播放列表
    public final List<MediaDatabaseHelper.MediaItem> currentPlaylist = new ArrayList<>();

    // 当前播放索引
    public int currentIndex = -1;
    
    // 媒体播放器
    private MediaPlayer mediaPlayer;
    
    // 播放状态
    private boolean isPlaying = false;
    private boolean isPaused = false;
    private boolean isBuffering = false;
    
    // 播放模式
    public enum PlayMode {
        SEQUENTIAL,  // 顺序播放
        RANDOM,      // 随机播放
        REPEAT_ONE,  // 单曲循环
        REPEAT_ALL   // 列表循环
    }
    
    private PlayMode currentPlayMode = PlayMode.SEQUENTIAL;
    
    // 当前播放的媒体项
    public MediaDatabaseHelper.MediaItem currentMediaItem;

    // 错误信息
    public String lastErrorMessage = "";
    
    // 处理器，用于延迟操作
    private final Handler handler = new Handler(Looper.getMainLooper());
    
    // 音频焦点辅助类
    private AudioFocusHelper audioFocusHelper;
    

    
    /**
     * 构造函数
     */
    public LocalMediaPlayer() {
        super("LocalMediaPlayer", "小萝卜故事机");

        // 初始化数据库
        dbHelper = new MediaDatabaseHelper(AssistantApplication.getContext());

        // 初始化媒体根目录
        initMediaRootDirectory();

        // 初始化音频焦点辅助类
        audioFocusHelper = new AudioFocusHelper(AssistantApplication.getContext());

        // 扫描并建立媒体数据库
        scanAndBuildDatabase();

        // 初始化媒体播放器
        initMediaPlayer();

        // 初始化设备属性
        initProperties();

        // 初始化设备方法（儿童友好的方法名）
        initChildFriendlyMethods();
    }
    
    /**
     * 初始化媒体根目录
     */
    private void initMediaRootDirectory() {
        // 优先使用指定的SD卡路径
        mediaRootDir = new File(MEDIA_ROOT_PATH);

        if (mediaRootDir.exists()) {
            Log.i(TAG, "使用指定媒体目录: " + mediaRootDir.getAbsolutePath());
            return;
        }

        // 备用方案1: 使用标准外部存储路径
        File standardPath = new File("/storage/emulated/0/Media");
        if (standardPath.exists()) {
            mediaRootDir = standardPath;
            Log.i(TAG, "使用标准外部存储媒体目录: " + mediaRootDir.getAbsolutePath());
            return;
        }

        // 备用方案2: 使用Environment.getExternalStorageDirectory()
        File sdCardRoot = android.os.Environment.getExternalStorageDirectory();
        if (sdCardRoot != null) {
            File sdCardMediaDir = new File(sdCardRoot, "Media");
            if (sdCardMediaDir.exists()) {
                mediaRootDir = sdCardMediaDir;
                Log.i(TAG, "使用SD卡媒体目录: " + mediaRootDir.getAbsolutePath());
                return;
            }
        }

        // 最后备用方案: 使用应用外部文件目录
        Context context = AssistantApplication.getContext();
        File externalDir = context.getExternalFilesDir(null);
        if (externalDir != null) {
            mediaRootDir = new File(externalDir.getParent(), "Media");
        } else {
            mediaRootDir = new File(context.getFilesDir(), "Media");
        }

        Log.i(TAG, "媒体根目录: " + mediaRootDir.getAbsolutePath());
        Log.i(TAG, "媒体根目录存在: " + mediaRootDir.exists());

        // 如果目录不存在，创建示例目录结构
        if (!mediaRootDir.exists()) {
            createSampleMediaStructure();
        }
    }

    /**
     * 创建示例媒体目录结构（用于演示）
     */
    private void createSampleMediaStructure() {
        try {
            mediaRootDir.mkdirs();

            // 创建示例分类目录
            File storiesDir = new File(mediaRootDir, "儿童故事《夏风入梦精选1047篇》");
            File songsDir = new File(mediaRootDir, "宝宝巴士儿歌大全586首");
            File tacticsDir = new File(mediaRootDir, "儿童睡前故事《讲给孩子的三十六计》");

            storiesDir.mkdirs();
            songsDir.mkdirs();
            tacticsDir.mkdirs();

            Log.i(TAG, "已创建示例媒体目录结构");
            Log.i(TAG, "请将媒体文件复制到: " + mediaRootDir.getAbsolutePath());

        } catch (Exception e) {
            Log.e(TAG, "创建示例媒体目录失败", e);
        }
    }

    /**
     * 扫描媒体文件并建立数据库
     */
    private void scanAndBuildDatabase() {
        if (!mediaRootDir.exists()) {
            Log.w(TAG, "媒体根目录不存在，无法扫描媒体文件");
            return;
        }

        Log.i(TAG, "开始扫描媒体文件并建立数据库...");

        // 清空现有数据
        dbHelper.clearAllData();

        // 扫描目录
        scanDirectoryToDatabase(mediaRootDir, "");

        // 更新缓存
        updateMediaCache();

        Log.i(TAG, "媒体数据库建立完成");
    }

    /**
     * 递归扫描目录并存入数据库
     */
    private void scanDirectoryToDatabase(File directory, String categoryPrefix) {
        File[] files = directory.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                // 递归扫描子目录
                String subCategory = categoryPrefix.isEmpty() ? file.getName() : categoryPrefix + "/" + file.getName();

                // 创建分类信息
                MediaDatabaseHelper.CategoryInfo categoryInfo = new MediaDatabaseHelper.CategoryInfo();
                categoryInfo.name = subCategory;
                categoryInfo.type = getCategoryType(file.getName());
                categoryInfo.description = getCategoryDescription(file.getName());

                dbHelper.insertOrUpdateCategory(categoryInfo);

                scanDirectoryToDatabase(file, subCategory);
            } else if (isAudioFile(file)) {
                // 处理音频文件
                String category = categoryPrefix.isEmpty() ? "未分类" : categoryPrefix;
                String displayName = getDisplayName(file.getName());

                MediaDatabaseHelper.MediaItem item = new MediaDatabaseHelper.MediaItem();
                item.fileName = file.getName();
                item.displayName = displayName;
                item.category = category;
                item.filePath = file.getAbsolutePath();
                item.tags = generateTags(displayName, category);
                item.description = generateDescription(displayName, category);
                item.ageGroup = getAgeGroup(category);

                dbHelper.insertMediaItem(item);
            }
        }
    }

    /**
     * 获取分类类型
     */
    private String getCategoryType(String categoryName) {
        if (categoryName.contains("故事")) {
            return "story";
        } else if (categoryName.contains("儿歌") || categoryName.contains("歌曲")) {
            return "song";
        } else if (categoryName.contains("三十六计") || categoryName.contains("教育")) {
            return "education";
        } else {
            return "other";
        }
    }

    /**
     * 获取分类描述
     */
    private String getCategoryDescription(String categoryName) {
        if (categoryName.contains("夏风入梦")) {
            return "精选儿童睡前故事，帮助宝宝安然入睡";
        } else if (categoryName.contains("宝宝巴士")) {
            return "寓教于乐的儿童歌曲，培养宝宝好习惯";
        } else if (categoryName.contains("三十六计")) {
            return "中华智慧故事，启发宝宝思维";
        } else {
            return "优质儿童音频内容";
        }
    }

    /**
     * 生成标签
     */
    private String generateTags(String displayName, String category) {
        StringBuilder tags = new StringBuilder();

        // 基于分类生成标签
        if (category.contains("故事")) {
            tags.append("故事,睡前,童话");
        } else if (category.contains("儿歌")) {
            tags.append("儿歌,音乐,歌曲");
        } else if (category.contains("三十六计")) {
            tags.append("教育,智慧,成语");
        }

        // 基于内容生成标签
        if (displayName.contains("小红帽")) {
            tags.append(",经典童话");
        } else if (displayName.contains("安全")) {
            tags.append(",安全教育");
        } else if (displayName.contains("习惯")) {
            tags.append(",好习惯");
        }

        return tags.toString();
    }

    /**
     * 生成描述
     */
    private String generateDescription(String displayName, String category) {
        if (category.contains("故事")) {
            return "精彩的" + displayName + "故事，适合睡前聆听";
        } else if (category.contains("儿歌")) {
            return "好听的" + displayName + "儿歌，寓教于乐";
        } else if (category.contains("三十六计")) {
            return "智慧的" + displayName + "故事，启发思维";
        } else {
            return displayName;
        }
    }

    /**
     * 获取适合年龄组
     */
    private String getAgeGroup(String category) {
        if (category.contains("宝宝巴士")) {
            return "2-6岁";
        } else if (category.contains("三十六计")) {
            return "6-12岁";
        } else {
            return "3-8岁";
        }
    }

    /**
     * 更新媒体缓存
     */
    private void updateMediaCache() {
        mediaCategories.clear();

        // 从数据库加载所有分类
        List<MediaDatabaseHelper.CategoryInfo> categories = dbHelper.getAllCategories();

        for (MediaDatabaseHelper.CategoryInfo category : categories) {
            List<MediaDatabaseHelper.MediaItem> items = dbHelper.getMediaByCategory(category.name);
            mediaCategories.put(category.name, items);
        }

        Log.i(TAG, "媒体缓存更新完成，共 " + mediaCategories.size() + " 个分类");

        for (String category : mediaCategories.keySet()) {
            Log.i(TAG, "分类 [" + category + "] 包含 " + mediaCategories.get(category).size() + " 个文件");
        }
    }
    
    /**
     * 判断是否为音频文件
     */
    private boolean isAudioFile(File file) {
        String name = file.getName().toLowerCase();
        return name.endsWith(".mp3") || name.endsWith(".wav") || 
               name.endsWith(".m4a") || name.endsWith(".aac") || 
               name.endsWith(".ogg") || name.endsWith(".flac");
    }
    
    /**
     * 从文件名获取显示名称
     */
    private String getDisplayName(String fileName) {
        // 移除文件扩展名
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            fileName = fileName.substring(0, lastDot);
        }
        
        // 移除编号前缀（如"0001集 "）
        fileName = fileName.replaceFirst("^\\d+集\\s*", "");
        
        return fileName;
    }
    
    /**
     * 初始化媒体播放器
     */
    private void initMediaPlayer() {
        mediaPlayer = new MediaPlayer();
        
        // 使用AudioAttributes代替过时的setAudioStreamType
        AudioAttributes audioAttributes = new AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_MEDIA)
            .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
            .build();
        mediaPlayer.setAudioAttributes(audioAttributes);
        
        // 设置播放完成监听器
        mediaPlayer.setOnCompletionListener(mp -> {
            Log.i(TAG, "播放完成: " + (currentMediaItem != null ? currentMediaItem.getDisplayName() : "未知"));
            handlePlaybackCompletion();
        });
        
        // 设置错误监听器
        mediaPlayer.setOnErrorListener((mp, what, extra) -> {
            String errorMsg = "播放错误: what=" + what + ", extra=" + extra;
            Log.e(TAG, errorMsg);
            lastErrorMessage = errorMsg;
            
            isPlaying = false;
            isBuffering = false;
            isPaused = false;
            
            // 释放音频焦点
            audioFocusHelper.abandonAudioFocus();
            
            return false;
        });
        
        // 设置准备完成监听器
        mediaPlayer.setOnPreparedListener(mp -> {
            Log.i(TAG, "媒体准备完成，开始播放: " + (currentMediaItem != null ? currentMediaItem.getDisplayName() : "未知"));
            
            // 获取音频焦点后开始播放
            if (audioFocusHelper.requestAudioFocus()) {
                mp.start();
                isPlaying = true;
                isPaused = false;
                isBuffering = false;
                lastErrorMessage = "";
            } else {
                Log.e(TAG, "无法获取音频焦点，播放失败");
                isPlaying = false;
            }
        });
        
        // 设置缓冲更新监听器
        mediaPlayer.setOnBufferingUpdateListener((mp, percent) -> {
            isBuffering = (percent < 100);
        });
    }

    /**
     * 处理播放完成事件
     */
    private void handlePlaybackCompletion() {
        isPlaying = false;
        isPaused = false;
        isBuffering = false;

        // 释放音频焦点
        audioFocusHelper.abandonAudioFocus();

        // 根据播放模式决定下一步操作
        switch (currentPlayMode) {
            case REPEAT_ONE:
                // 单曲循环，重新播放当前歌曲
                playCurrentItem();
                break;
            case SEQUENTIAL:
            case REPEAT_ALL:
                // 顺序播放或列表循环，播放下一首
                playNext();
                break;
            case RANDOM:
                // 随机播放，随机选择下一首
                playRandomItem();
                break;
        }
    }

    /**
     * 初始化设备属性（优化数据量，适合发送给服务端）
     */
    private void initProperties() {
        // 播放状态属性
        properties.addBooleanProperty("isPlaying", "小萝卜是否在播放", () -> isPlaying);
        properties.addBooleanProperty("isPaused", "小萝卜是否暂停了", () -> isPaused);
        properties.addBooleanProperty("isBuffering", "小萝卜是否在加载", () -> isBuffering);

        // 当前播放信息属性
        properties.addStringProperty("currentStory", "正在播放的故事", () ->
            currentMediaItem != null ? currentMediaItem.displayName : "没有播放");
        properties.addStringProperty("currentCategory", "正在播放的分类", () ->
            currentMediaItem != null ? currentMediaItem.category : "没有分类");

        // 播放模式属性
        properties.addStringProperty("playMode", "播放模式", () -> getPlayModeString());

        // 播放列表信息属性
        properties.addNumberProperty("playlistSize", "播放列表大小", () -> currentPlaylist.size());
        properties.addNumberProperty("currentIndex", "当前播放位置", () -> currentIndex + 1); // 从1开始计数，更友好

        // 媒体库统计信息（精简版，减少数据量）
        properties.addStringProperty("libraryStats", "故事库统计", this::getLibraryStatsString);

        // 分类摘要（只发送分类名称和数量，不发送具体文件列表）
        properties.addStringProperty("categorySummary", "分类摘要", this::getCategorySummaryString);

        // 推荐内容（基于AI检索需求）
        properties.addStringProperty("storyKeywords", "故事关键词", this::getStoryKeywordsString);
        properties.addStringProperty("songKeywords", "儿歌关键词", this::getSongKeywordsString);

        // 错误信息属性
        properties.addStringProperty("errorStatus", "播放状态", () ->
            lastErrorMessage.isEmpty() ? "一切正常" : lastErrorMessage);
    }

    /**
     * 获取媒体库统计信息字符串（精简版）
     */
    private String getLibraryStatsString() {
        Map<String, Integer> stats = dbHelper.getCategoryStats();
        int totalStories = 0;
        int totalSongs = 0;
        int totalEducation = 0;

        for (Map.Entry<String, Integer> entry : stats.entrySet()) {
            String category = entry.getKey();
            int count = entry.getValue();

            if (category.contains("故事")) {
                totalStories += count;
            } else if (category.contains("儿歌")) {
                totalSongs += count;
            } else if (category.contains("三十六计")) {
                totalEducation += count;
            }
        }

        return String.format("故事:%d首,儿歌:%d首,教育:%d首", totalStories, totalSongs, totalEducation);
    }

    /**
     * 获取分类摘要字符串（只包含分类名称，不包含具体文件）
     */
    private String getCategorySummaryString() {
        StringBuilder sb = new StringBuilder();
        List<MediaDatabaseHelper.CategoryInfo> categories = dbHelper.getAllCategories();

        for (MediaDatabaseHelper.CategoryInfo category : categories) {
            if (sb.length() > 0) sb.append(",");
            sb.append(category.name).append("(").append(category.mediaCount).append(")");
        }

        return sb.toString();
    }

    /**
     * 获取故事关键词（用于AI检索）
     */
    private String getStoryKeywordsString() {
        return "小红帽,白雪公主,灰姑娘,三只小猪,龟兔赛跑,小蝌蚪找妈妈,狼来了,小兔子乖乖,胡桃夹子,木偶奇遇记,安徒生童话,格林童话,睡前故事,童话故事";
    }

    /**
     * 获取儿歌关键词（用于AI检索）
     */
    private String getSongKeywordsString() {
        return "宝宝巴士,儿歌,童谣,好习惯,安全歌,数字歌,颜色歌,动物歌,交通工具,职业体验,古诗词,三字经,弟子规";
    }

    /**
     * 获取播放模式字符串
     */
    private String getPlayModeString() {
        switch (currentPlayMode) {
            case SEQUENTIAL: return "顺序播放";
            case RANDOM: return "随机播放";
            case REPEAT_ONE: return "单曲循环";
            case REPEAT_ALL: return "列表循环";
            default: return "未知";
        }
    }

    /**
     * 获取分类列表的字符串表示
     */
    private String getCategoriesString() {
        StringBuilder sb = new StringBuilder();
        sb.append("媒体分类（共 ").append(mediaCategories.size()).append(" 个分类）：\n\n");

        for (String category : mediaCategories.keySet()) {
            sb.append(category).append("（").append(mediaCategories.get(category).size()).append(" 个文件）\n");
        }
        return sb.toString();
    }

    /**
     * 获取指定分类媒体列表的字符串表示
     */
    private String getCategoryMediaString(String category) {
        if (!mediaCategories.containsKey(category)) {
            return "分类 '" + category + "' 不存在";
        }

        List<MediaItem> items = mediaCategories.get(category);
        if (items.isEmpty()) {
            return "分类 '" + category + "' 中没有媒体文件";
        }

        StringBuilder sb = new StringBuilder();
        sb.append(category).append("（").append(items.size()).append(" 个文件）：\n\n");

        for (int i = 0; i < Math.min(items.size(), 20); i++) { // 限制显示前20个
            MediaItem item = items.get(i);
            sb.append((i + 1)).append(". ").append(item.getDisplayName()).append("\n");
        }

        if (items.size() > 20) {
            sb.append("... 还有 ").append(items.size() - 20).append(" 个文件\n");
        }

        return sb.toString();
    }

    /**
     * 初始化儿童友好的设备方法
     */
    private void initChildFriendlyMethods() {
        // 播放控制方法（儿童友好命名）
        initChildFriendlyPlaybackMethods();

        // 故事和儿歌选择方法
        initStoryAndSongMethods();

        // 播放模式控制方法
        initPlayModeMethods();

        // 智能搜索方法
        initSmartSearchMethods();
    }

    /**
     * 初始化儿童友好的播放控制方法
     */
    private void initChildFriendlyPlaybackMethods() {
        // 播放方法
        methods.addMethod("startPlaying", "小萝卜开始播放", new Parameter.ParameterList(), parameters -> {
            play();
        });

        // 暂停方法
        methods.addMethod("pauseStory", "小萝卜暂停一下", new Parameter.ParameterList(), parameters -> {
            pause();
        });

        // 停止方法
        methods.addMethod("stopPlaying", "小萝卜停止播放", new Parameter.ParameterList(), parameters -> {
            stop();
        });

        // 恢复播放方法
        methods.addMethod("continueStory", "小萝卜继续播放", new Parameter.ParameterList(), parameters -> {
            resume();
        });

        // 下一首方法
        methods.addMethod("nextStory", "小萝卜播放下一个", new Parameter.ParameterList(), parameters -> {
            playNext();
        });

        // 上一首方法
        methods.addMethod("previousStory", "小萝卜播放上一个", new Parameter.ParameterList(), parameters -> {
            playPrevious();
        });

        // 重新播放当前故事
        methods.addMethod("replayStory", "小萝卜重新播放这个", new Parameter.ParameterList(), parameters -> {
            LocalMediaPlayerMethods.replayCurrentStory(this);
        });
    }

    /**
     * 初始化故事和儿歌选择方法
     */
    private void initStoryAndSongMethods() {
        // 播放睡前故事
        methods.addMethod("playBedtimeStories", "小萝卜讲睡前故事", new Parameter.ParameterList(), parameters -> {
            playCategory("儿童故事《夏风入梦精选1047篇》");
        });

        // 播放儿歌
        methods.addMethod("playSongs", "小萝卜唱儿歌", new Parameter.ParameterList(), parameters -> {
            playCategory("宝宝巴士儿歌大全586首");
        });

        // 播放教育故事
        methods.addMethod("playEducationStories", "小萝卜讲教育故事", new Parameter.ParameterList(), parameters -> {
            playCategory("儿童睡前故事《讲给孩子的三十六计》");
        });

        // 播放指定故事
        List<Parameter> playStoryParams = new ArrayList<>();
        playStoryParams.add(new Parameter("storyName", "故事名称", ValueType.STRING, true));
        Parameter.ParameterList playStoryParamList = new Parameter.ParameterList(playStoryParams);

        methods.addMethod("tellStory", "小萝卜讲指定故事", playStoryParamList, parameters -> {
            String storyName = parameters.getParameter("storyName").getString();
            playMediaByName(storyName);
        });

        // 播放指定儿歌
        List<Parameter> playSongParams = new ArrayList<>();
        playSongParams.add(new Parameter("songName", "儿歌名称", ValueType.STRING, true));
        Parameter.ParameterList playSongParamList = new Parameter.ParameterList(playSongParams);

        methods.addMethod("singSong", "小萝卜唱指定儿歌", playSongParamList, parameters -> {
            String songName = parameters.getParameter("songName").getString();
            playMediaByName(songName);
        });

        // 随机播放故事
        methods.addMethod("tellRandomStory", "小萝卜随机讲故事", new Parameter.ParameterList(), parameters -> {
            LocalMediaPlayerMethods.playRandomFromCategory(this, "story");
        });

        // 随机播放儿歌
        methods.addMethod("singRandomSong", "小萝卜随机唱儿歌", new Parameter.ParameterList(), parameters -> {
            LocalMediaPlayerMethods.playRandomFromCategory(this, "song");
        });

        // 播放经典童话
        methods.addMethod("playClassicTales", "小萝卜讲经典童话", new Parameter.ParameterList(), parameters -> {
            LocalMediaPlayerMethods.searchAndPlay(this, "童话");
        });

        // 播放安全教育内容
        methods.addMethod("playSafetyEducation", "小萝卜讲安全知识", new Parameter.ParameterList(), parameters -> {
            LocalMediaPlayerMethods.searchAndPlay(this, "安全");
        });
    }

    /**
     * 初始化播放模式控制方法
     */
    private void initPlayModeMethods() {
        // 设置播放模式方法
        List<Parameter> setModeParams = new ArrayList<>();
        setModeParams.add(new Parameter("mode", "播放模式(sequential/random/repeat_one/repeat_all)", ValueType.STRING, true));
        Parameter.ParameterList setModeParamList = new Parameter.ParameterList(setModeParams);

        methods.addMethod("setPlayMode", "设置播放模式", setModeParamList, parameters -> {
            String mode = parameters.getParameter("mode").getString();
            setPlayMode(mode);
        });
    }

    /**
     * 初始化智能搜索方法
     */
    private void initSmartSearchMethods() {
        // 智能搜索并播放
        List<Parameter> smartSearchParams = new ArrayList<>();
        smartSearchParams.add(new Parameter("content", "想听的内容", ValueType.STRING, true));
        Parameter.ParameterList smartSearchParamList = new Parameter.ParameterList(smartSearchParams);

        methods.addMethod("findAndPlay", "小萝卜找到并播放", smartSearchParamList, parameters -> {
            String content = parameters.getParameter("content").getString();
            LocalMediaPlayerMethods.smartSearchAndPlay(this, content);
        });

        // 按年龄播放内容
        List<Parameter> ageParams = new ArrayList<>();
        ageParams.add(new Parameter("age", "年龄", ValueType.STRING, true));
        Parameter.ParameterList ageParamList = new Parameter.ParameterList(ageParams);

        methods.addMethod("playForAge", "小萝卜播放适合年龄的内容", ageParamList, parameters -> {
            String age = parameters.getParameter("age").getString();
            LocalMediaPlayerMethods.playForAgeGroup(this, age);
        });

        // 按主题播放内容
        List<Parameter> themeParams = new ArrayList<>();
        themeParams.add(new Parameter("theme", "主题", ValueType.STRING, true));
        Parameter.ParameterList themeParamList = new Parameter.ParameterList(themeParams);

        methods.addMethod("playByTheme", "小萝卜播放主题内容", themeParamList, parameters -> {
            String theme = parameters.getParameter("theme").getString();
            LocalMediaPlayerMethods.playByTheme(this, theme);
        });

        // 刷新故事库
        methods.addMethod("refreshStoryLibrary", "小萝卜更新故事库", new Parameter.ParameterList(), parameters -> {
            refreshMediaLibrary();
        });

        // 获取推荐内容
        methods.addMethod("getRecommendations", "小萝卜推荐内容", new Parameter.ParameterList(), parameters -> {
            LocalMediaPlayerMethods.getRecommendations(this);
        });
    }

    // ==================== 播放控制实现 ====================

    /**
     * 开始播放
     */
    public void play() {
        if (currentPlaylist.isEmpty()) {
            Log.w(TAG, "播放列表为空，无法播放");
            return;
        }

        if (currentIndex < 0 || currentIndex >= currentPlaylist.size()) {
            currentIndex = 0;
        }

        playCurrentItem();
    }

    /**
     * 播放当前项
     */
    public void playCurrentItem() {
        if (currentIndex < 0 || currentIndex >= currentPlaylist.size()) {
            Log.w(TAG, "无效的播放索引: " + currentIndex);
            return;
        }

        currentMediaItem = currentPlaylist.get(currentIndex);

        try {
            // 停止当前播放
            if (isPlaying) {
                mediaPlayer.stop();
            }

            // 重置媒体播放器
            mediaPlayer.reset();

            // 设置播放源
            File mediaFile = new File(currentMediaItem.getFilePath());
            if (!mediaFile.exists()) {
                Log.e(TAG, "媒体文件不存在: " + currentMediaItem.getFilePath());
                lastErrorMessage = "媒体文件不存在: " + currentMediaItem.getDisplayName();
                return;
            }

            Uri uri = Uri.fromFile(mediaFile);
            mediaPlayer.setDataSource(AssistantApplication.getContext(), uri);

            Log.i(TAG, "准备播放: " + currentMediaItem.getDisplayName());

            // 异步准备
            mediaPlayer.prepareAsync();
            isBuffering = true;

        } catch (IOException e) {
            String errorMsg = "设置播放源失败: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            lastErrorMessage = errorMsg;
            isPlaying = false;
            isBuffering = false;
        } catch (Exception e) {
            String errorMsg = "未预期的错误: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            lastErrorMessage = errorMsg;
            isPlaying = false;
            isBuffering = false;
        }
    }

    /**
     * 暂停播放
     */
    public void pause() {
        if (mediaPlayer != null && isPlaying) {
            try {
                mediaPlayer.pause();
                isPlaying = false;
                isPaused = true;
                // 释放音频焦点
                audioFocusHelper.abandonAudioFocus();
                Log.i(TAG, "暂停播放: " + (currentMediaItem != null ? currentMediaItem.getDisplayName() : "未知"));
            } catch (Exception e) {
                Log.e(TAG, "暂停播放失败", e);
            }
        }
    }

    /**
     * 停止播放
     */
    public void stop() {
        if (mediaPlayer != null) {
            try {
                if (isPlaying) {
                    mediaPlayer.stop();
                }
                isPlaying = false;
                isPaused = false;
                isBuffering = false;
                currentIndex = -1;
                currentMediaItem = null;
                // 释放音频焦点
                audioFocusHelper.abandonAudioFocus();
                Log.i(TAG, "停止播放");
            } catch (Exception e) {
                Log.e(TAG, "停止播放失败", e);
            }
        }
    }

    /**
     * 恢复播放
     */
    public void resume() {
        if (mediaPlayer != null && isPaused && currentMediaItem != null) {
            try {
                // 获取音频焦点后开始播放
                if (audioFocusHelper.requestAudioFocus()) {
                    mediaPlayer.start();
                    isPlaying = true;
                    isPaused = false;
                    Log.i(TAG, "恢复播放: " + currentMediaItem.getDisplayName());
                } else {
                    Log.e(TAG, "无法获取音频焦点，恢复失败");
                }
            } catch (Exception e) {
                Log.e(TAG, "恢复播放失败", e);
            }
        }
    }

    /**
     * 播放下一首
     */
    public void playNext() {
        if (currentPlaylist.isEmpty()) {
            Log.w(TAG, "播放列表为空，无法播放下一首");
            return;
        }

        switch (currentPlayMode) {
            case SEQUENTIAL:
                if (currentIndex < currentPlaylist.size() - 1) {
                    currentIndex++;
                    playCurrentItem();
                } else {
                    Log.i(TAG, "已是最后一首，停止播放");
                    stop();
                }
                break;
            case REPEAT_ALL:
                currentIndex = (currentIndex + 1) % currentPlaylist.size();
                playCurrentItem();
                break;
            case RANDOM:
                playRandomItem();
                break;
            case REPEAT_ONE:
                // 单曲循环模式下，下一首就是当前歌曲
                playCurrentItem();
                break;
        }
    }

    /**
     * 播放上一首
     */
    public void playPrevious() {
        if (currentPlaylist.isEmpty()) {
            Log.w(TAG, "播放列表为空，无法播放上一首");
            return;
        }

        switch (currentPlayMode) {
            case SEQUENTIAL:
                if (currentIndex > 0) {
                    currentIndex--;
                    playCurrentItem();
                } else {
                    Log.i(TAG, "已是第一首，无法播放上一首");
                }
                break;
            case REPEAT_ALL:
                currentIndex = (currentIndex - 1 + currentPlaylist.size()) % currentPlaylist.size();
                playCurrentItem();
                break;
            case RANDOM:
                playRandomItem();
                break;
            case REPEAT_ONE:
                // 单曲循环模式下，上一首就是当前歌曲
                playCurrentItem();
                break;
        }
    }

    /**
     * 随机播放一首
     */
    private void playRandomItem() {
        if (currentPlaylist.isEmpty()) {
            Log.w(TAG, "播放列表为空，无法随机播放");
            return;
        }

        int randomIndex = (int) (Math.random() * currentPlaylist.size());
        currentIndex = randomIndex;
        playCurrentItem();
    }

    // ==================== 播放列表管理实现 ====================

    /**
     * 播放指定分类
     */
    public void playCategory(String category) {
        if (!mediaCategories.containsKey(category)) {
            Log.e(TAG, "分类不存在: " + category);
            lastErrorMessage = "分类不存在: " + category;
            return;
        }

        List<MediaItem> categoryItems = mediaCategories.get(category);
        if (categoryItems.isEmpty()) {
            Log.e(TAG, "分类中没有媒体文件: " + category);
            lastErrorMessage = "分类中没有媒体文件: " + category;
            return;
        }

        // 设置播放列表
        currentPlaylist = new ArrayList<>(categoryItems);
        currentIndex = 0;

        Log.i(TAG, "设置播放列表为分类: " + category + "，共 " + currentPlaylist.size() + " 个文件");

        // 开始播放
        playCurrentItem();
    }

    /**
     * 通过名称播放媒体
     */
    public void playMediaByName(String mediaName) {
        // 使用数据库搜索
        List<MediaDatabaseHelper.MediaItem> searchResults = dbHelper.searchMedia(mediaName);

        if (searchResults.isEmpty()) {
            Log.e(TAG, "未找到媒体: " + mediaName);
            lastErrorMessage = "未找到媒体: " + mediaName;
            return;
        }

        // 使用第一个搜索结果
        MediaDatabaseHelper.MediaItem foundItem = searchResults.get(0);

        // 设置播放列表为单个文件
        currentPlaylist.clear();
        currentPlaylist.add(foundItem);
        currentIndex = 0;

        Log.i(TAG, "播放媒体: " + foundItem.displayName);

        // 开始播放
        playCurrentItem();
    }

    /**
     * 随机播放所有媒体
     */
    public void playAllRandom() {
        if (allMediaItems.isEmpty()) {
            Log.w(TAG, "媒体库为空，无法播放");
            return;
        }

        // 设置播放列表为所有媒体
        currentPlaylist = new ArrayList<>(allMediaItems);

        // 打乱播放列表
        Collections.shuffle(currentPlaylist);
        currentIndex = 0;

        Log.i(TAG, "随机播放所有媒体，共 " + currentPlaylist.size() + " 个文件");

        // 开始播放
        playCurrentItem();
    }

    /**
     * 清空播放列表
     */
    public void clearPlaylist() {
        stop();
        currentPlaylist.clear();
        currentIndex = -1;
        currentMediaItem = null;
        Log.i(TAG, "播放列表已清空");
    }

    // ==================== 播放模式控制实现 ====================

    /**
     * 设置播放模式
     */
    public void setPlayMode(String mode) {
        PlayMode newMode;

        switch (mode.toLowerCase()) {
            case "sequential":
                newMode = PlayMode.SEQUENTIAL;
                break;
            case "random":
                newMode = PlayMode.RANDOM;
                break;
            case "repeat_one":
                newMode = PlayMode.REPEAT_ONE;
                break;
            case "repeat_all":
                newMode = PlayMode.REPEAT_ALL;
                break;
            default:
                Log.e(TAG, "无效的播放模式: " + mode);
                lastErrorMessage = "无效的播放模式: " + mode;
                return;
        }

        currentPlayMode = newMode;
        Log.i(TAG, "播放模式已设置为: " + getPlayModeString());
    }

    // ==================== 媒体搜索和管理实现 ====================

    /**
     * 搜索媒体
     */
    public void searchMedia(String keyword) {
        List<MediaItem> searchResults = new ArrayList<>();

        for (MediaItem item : allMediaItems) {
            if (item.getDisplayName().toLowerCase().contains(keyword.toLowerCase()) ||
                item.getCategory().toLowerCase().contains(keyword.toLowerCase())) {
                searchResults.add(item);
            }
        }

        Log.i(TAG, "搜索关键词 '" + keyword + "' 找到 " + searchResults.size() + " 个结果");

        if (!searchResults.isEmpty()) {
            // 设置播放列表为搜索结果
            currentPlaylist = searchResults;
            currentIndex = 0;

            // 开始播放第一个搜索结果
            playCurrentItem();
        } else {
            lastErrorMessage = "未找到包含关键词 '" + keyword + "' 的媒体";
        }
    }

    /**
     * 刷新媒体库
     */
    public void refreshMediaLibrary() {
        Log.i(TAG, "刷新媒体库...");

        // 停止当前播放
        stop();

        // 清空现有数据
        allMediaItems.clear();
        mediaCategories.clear();
        currentPlaylist.clear();

        // 重新扫描媒体文件
        scanMediaFiles();

        // 重新初始化属性（为新分类添加属性）
        initProperties();

        Log.i(TAG, "媒体库刷新完成");
    }

    /**
     * 在Thing被销毁时调用，释放资源
     */
    public void onDestroy() {
        // 移除所有挂起的回调
        handler.removeCallbacksAndMessages(null);

        // 释放音频焦点
        if (audioFocusHelper != null) {
            audioFocusHelper.abandonAudioFocus();
        }

        // 释放媒体播放器
        if (mediaPlayer != null) {
            try {
                if (isPlaying) {
                    mediaPlayer.stop();
                }
                mediaPlayer.release();
                mediaPlayer = null;
                isPlaying = false;
                isPaused = false;
                isBuffering = false;
                Log.d(TAG, "媒体播放器已释放");
            } catch (Exception e) {
                Log.e(TAG, "释放媒体播放器失败", e);
            }
        }
    }

    /**
     * 音频焦点管理助手类
     * 复用NetworkRadio中的实现
     */
    private static class AudioFocusHelper {
        private android.media.AudioManager audioManager;
        private android.media.AudioManager.OnAudioFocusChangeListener audioFocusChangeListener;
        private AtomicInteger audioFocusState = new AtomicInteger(android.media.AudioManager.AUDIOFOCUS_LOSS);

        public AudioFocusHelper(android.content.Context context) {
            audioManager = (android.media.AudioManager) context.getSystemService(android.content.Context.AUDIO_SERVICE);

            // 创建音频焦点变化监听器
            audioFocusChangeListener = focusChange -> {
                audioFocusState.set(focusChange);
                Log.d("AudioFocusHelper", "音频焦点变化: " + getAudioFocusStateString(focusChange));
            };
        }

        /**
         * 请求音频焦点
         *
         * @return 是否成功获取音频焦点
         */
        public boolean requestAudioFocus() {
            int result = audioManager.requestAudioFocus(
                audioFocusChangeListener,
                android.media.AudioManager.STREAM_MUSIC,
                android.media.AudioManager.AUDIOFOCUS_GAIN
            );

            boolean success = (result == android.media.AudioManager.AUDIOFOCUS_REQUEST_GRANTED);
            if (success) {
                audioFocusState.set(android.media.AudioManager.AUDIOFOCUS_GAIN);
            }

            Log.d("AudioFocusHelper", "请求音频焦点: " + (success ? "成功" : "失败"));
            return success;
        }

        /**
         * 放弃音频焦点
         */
        public void abandonAudioFocus() {
            audioManager.abandonAudioFocus(audioFocusChangeListener);
            audioFocusState.set(android.media.AudioManager.AUDIOFOCUS_LOSS);
            Log.d("AudioFocusHelper", "放弃音频焦点");
        }

        /**
         * 获取音频焦点状态字符串
         *
         * @param focusState 音频焦点状态
         * @return 状态描述
         */
        private String getAudioFocusStateString(int focusState) {
            switch (focusState) {
                case android.media.AudioManager.AUDIOFOCUS_GAIN:
                    return "AUDIOFOCUS_GAIN";
                case android.media.AudioManager.AUDIOFOCUS_LOSS:
                    return "AUDIOFOCUS_LOSS";
                case android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                    return "AUDIOFOCUS_LOSS_TRANSIENT";
                case android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK:
                    return "AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK";
                default:
                    return "UNKNOWN (" + focusState + ")";
            }
        }
    }
}
