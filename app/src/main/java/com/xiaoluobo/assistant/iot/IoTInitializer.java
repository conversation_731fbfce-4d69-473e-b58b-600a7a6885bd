package com.xiaoluobo.assistant.iot;

import android.content.Context;
import android.util.Log;

import com.xiaoluobo.assistant.iot.things.Battery;
import com.xiaoluobo.assistant.iot.things.EmotionDisplay;
import com.xiaoluobo.assistant.iot.things.LocalMediaPlayer;
import com.xiaoluobo.assistant.iot.things.NetworkRadio;
import com.xiaoluobo.assistant.iot.things.RobotMotion;
import com.xiaoluobo.assistant.iot.things.Speaker;

/**
 * IoT框架初始化类
 * 负责初始化IoT框架和注册设备
 */
public class IoTInitializer {
    private static final String TAG = "IoTInitializer";
    
    /**
     * 初始化IoT框架
     *
     * @param context 应用上下文
     */
    public static void initialize(Context context) {
        Log.i(TAG, "初始化IoT框架");
        
        // 获取设备管理器实例
        ThingManager manager = ThingManager.getInstance();
        
        // 注册设备
        registerThings(manager);
        
        Log.i(TAG, "IoT框架初始化完成，已注册设备数量: " + manager.getAllThings().size());
    }
    
    /**
     * 注册设备到设备管理器
     *
     * @param manager 设备管理器
     */
    private static void registerThings(ThingManager manager) {
        // 注册所有设备
        manager.addThing(new EmotionDisplay());
        manager.addThing(new Speaker());
        manager.addThing(new Battery());
//        manager.addThing(new NetworkRadio());
        manager.addThing(new RobotMotion());
        
        // 可以在这里注册更多设备
        // 例如：语音扬声器、电池管理、灯光控制等
    }
} 