package com.xiaoluobo.assistant.service;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Binder;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.PowerManager;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.xiaoluobo.assistant.MainActivity;
import com.xiaoluobo.assistant.R;
import com.xiaoluobo.assistant.audio.AudioProcessor;
import com.xiaoluobo.assistant.hardware.emotion.EMotionPlayer;
import com.xiaoluobo.assistant.iot.ThingManager;
import com.xiaoluobo.assistant.protocols.Protocol;
import com.xiaoluobo.assistant.protocols.ProtocolListener;
import com.xiaoluobo.assistant.protocols.ProtocolFactory;
import com.xiaoluobo.assistant.protocols.MqttProtocol;
import com.xiaoluobo.assistant.protocols.WebSocketProtocol;
import com.xiaoluobo.assistant.utils.DeviceStateManager;
import com.xiaoluobo.assistant.utils.GlobalMessageManager;
import com.xiaoluobo.assistant.ui.voicetest.MessageAdapter;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 语音通信服务
 * 使用协议层处理与服务器的通信
 */
public class VoiceCommunicationService extends Service {
    private static final String TAG = "VoiceCommunicationSvc";
    private static final String CHANNEL_ID = "VoiceCommunicationChannel";
    private static final int NOTIFICATION_ID = 3;
    
    // WAKEUP广播Action
    public static final String ACTION_WAKEUP = "com.xiaoluobo.assistant.WAKEUP";

    // 退出对话广播Action
    public static final String ACTION_EXIT_CHAT = "com.xiaoluobo.assistant.EXIT_AI_CHAT";

    // 唤醒服务通知广播Action
    public static final String ACTION_SERVICE_STARTED = "com.xiaoluobo.assistant.SERVICE_STARTED";
    
    // 默认配置
    private static final String DEFAULT_SERVER_URL = "wss://api.tenclass.net/xiaozhi/v1/";

    // 核心组件
    private Protocol protocol;
    private AudioProcessor audioProcessor;

    // 线程和资源管理
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private PowerManager.WakeLock wakeLock;
    
    // 监听器列表
    private final List<CommunicationListener> listeners = new ArrayList<>();

    // 状态变量
    private static final AtomicBoolean serviceRunning = new AtomicBoolean(false);

    // 状态管理
    private DeviceStateManager deviceState;

    // 单例
    private static VoiceCommunicationService instance;

    // 是否唤醒命令
    boolean fromWakeupCommand = false;

    /**
     * 通信事件类型
     */
    public enum EventType {
        CONNECTED,
        DISCONNECTED,
        TEXT_MESSAGE,
        AUDIO_MESSAGE,
        USER_MESSAGE,
        ERROR,
    }
    
    /**
     * 通信事件监听器接口
     */
    public interface CommunicationListener {
        void onEvent(EventType type, Object data);
    }
    
    /**
     * Binder for clients
     */
    public class LocalBinder extends Binder {
        public VoiceCommunicationService getService() {
            return VoiceCommunicationService.this;
        }
    }
    
    private final IBinder binder = new LocalBinder();
    
    /**
     * 检查服务是否在运行
     */
    public static boolean isRunning() {
        return serviceRunning.get();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "语音通信服务创建开始");

        this.deviceState = DeviceStateManager.getInstance();

        // 标记服务正在运行
        serviceRunning.set(true);
        
        // 创建通知渠道
        createNotificationChannel();
        
        try {
            // 初始化唤醒锁
            PowerManager powerManager = (PowerManager) getSystemService(POWER_SERVICE);
            wakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, 
                    "VoiceCommunicationService::WakeLock");
            wakeLock.acquire();
            
            // 初始化音频组件
            audioProcessor = new AudioProcessor(this);

            // 设置音频处理器的回调
            audioProcessor.setEncodedAudioCallback(data -> {
                if (protocol != null && protocol.isConnected()) {
                    protocol.sendAudio(data);
                }
            });

            // 创建广播接收器
            registerExitChatReceiver();
            registerWakeupReceiver();
            
            Log.i(TAG, "语音通信服务创建完成");
        } catch (Exception e) {
            Log.e(TAG, "初始化语音通信服务失败: " + e.getMessage(), e);
            stopSelf();
        }
        instance = this;
    }

    public static VoiceCommunicationService getInstance() {
        if (instance == null) {
            synchronized (VoiceCommunicationService.class) {
                if (instance == null) {
                    instance = new VoiceCommunicationService();
                }
            }
        }
        return instance;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (startId > 1) return START_STICKY; // 防止重复启动
        Log.i(TAG, "服务命令启动");

        // 检查是否是由唤醒词激活的启动
        boolean fromWakeup = intent != null && intent.getBooleanExtra("from_wakeup", false);
        if (fromWakeup) {
            Log.i(TAG, "服务由唤醒词激活启动");
            // 添加消息记录到全局消息管理器
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "服务由唤醒词触发启动")
            );
        } else {
            // 添加消息记录到全局消息管理器
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "语音交互服务启动")
            );
        }

        this.fromWakeupCommand = false;
        if (intent != null) {
            // 检查是否由唤醒触发
            this.fromWakeupCommand = intent.getBooleanExtra("from_wakeup", false);

            // 获取协议类型（默认使用MQTT）
            String protocolTypeStr = intent.getStringExtra("protocol_type");
            ProtocolFactory.ProtocolType protocolType = ProtocolFactory.ProtocolType.MQTT;
            if ("websocket".equalsIgnoreCase(protocolTypeStr)) {
                protocolType = ProtocolFactory.ProtocolType.WEBSOCKET;
            }
            
            // 创建协议实例
            protocol = ProtocolFactory.createProtocol(this, protocolType);
            
            // 设置协议监听器
            protocol.addListener(new ServiceProtocolListener());

            // 如果是MQTT协议，尝试通过OTA自动配置
            if (protocolType == ProtocolFactory.ProtocolType.MQTT && protocol instanceof MqttProtocol) {
                Log.i(TAG, "使用MQTT协议，通过OTA进行自动配置");
                MqttProtocol mqttProtocol = (MqttProtocol) protocol;
                mqttProtocol.autoConnectWithOta();
            } else {
                Log.i(TAG, "使用wss协议，通过OTA进行自动配置");
                WebSocketProtocol wssProtocol = (WebSocketProtocol) protocol;
                wssProtocol.autoConnectWithOta();
            }
        }

        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification());
        
        // 标记服务正在运行
        serviceRunning.set(true);

        // 如果有待处理的唤醒事件，或是由唤醒触发的启动，则在建立连接后处理
        if (this.fromWakeupCommand) {
            Log.i(TAG, "服务启动由唤醒触发，将在连接建立后处理唤醒");
            waitAudioChannelOpened();
        }

        // 发送服务已启动的广播
        Intent notifyIntent = new Intent(ACTION_SERVICE_STARTED);
        notifyIntent.setPackage(getPackageName());
        notifyIntent.putExtra("from_wakeup", this.fromWakeupCommand);
        sendBroadcast(notifyIntent);

        // 启动完毕更新空闲状态
        deviceState.setDeviceState(DeviceStateManager.DeviceState.IDLE);

        return START_STICKY;
    }
    
    /**
     * 添加通信监听器
     */
    public void addListener(CommunicationListener listener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }
    
    /**
     * 移除通信监听器
     */
    public void removeListener(CommunicationListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 分发事件到所有监听器
     */
    private void dispatchEvent(EventType type, Object data) {
        // 同时将消息记录到全局消息管理器
        if (type == EventType.TEXT_MESSAGE) {
            // TTS消息处理
            String message = (String) data;
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_TTS, message)
            );
        } else if (type == EventType.USER_MESSAGE) {
            // STT消息处理
            String message = (String) data;
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_STT, message)
            );
        } else if (type == EventType.CONNECTED) {
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "与服务器建立连接")
            );
        } else if (type == EventType.DISCONNECTED) {
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "与服务器断开连接")
            );
        } else if (type == EventType.ERROR) {
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "发生错误: " + data)
            );
        }
        
        // 分发给所有监听器
        for (CommunicationListener listener : new ArrayList<>(listeners)) {
            mainHandler.post(() -> listener.onEvent(type, data));
        }
    }
    
    /**
     * 连接到服务器
     */
    public void connect() {
        if (protocol == null) {
            Log.e(TAG, "协议未初始化");
            return;
        }
        
        if (protocol.isConnected()) {
            Log.d(TAG, "已经连接到服务器");
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "已经连接到服务器")
            );
            return;
        }

        Log.i(TAG, "开始连接到服务器");
        GlobalMessageManager.getInstance().addMessage(
            new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "正在连接到服务器...")
        );
        try {
            // 连接服务
            protocol.start();
        } catch (Exception e) {
            Log.e(TAG, "连接服务器失败: " + e.getMessage(), e);
        } finally {
            serviceRunning.set( true);
        }
    }
    
    /**
     * 断开与服务器的连接
     */
    public void disconnect() {
        if (protocol == null) {
            return;
        }
        
        try {
            // 断开连接
            protocol.disconnect();

            serviceRunning.set(false);

            // 更新通知
            updateNotification("通信助手已停止");

            Log.i(TAG, "断开与服务器的连接");
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "正在断开与服务器的连接...")
            );

        } catch (Exception e) {
            Log.e(TAG, "断开服务器连接失败: " + e.getMessage(), e);
        }

        Log.i(TAG, "语音通信服务状态: isRunning=" + serviceRunning.get());
    }

    /**
     * 停止所有音频活动
     */
    public void stopAudioPlaying() {
        if (protocol != null) {
            protocol.closeAudioChannel();
            Log.i(TAG, "已停止语音通信处理");
        }
        if (audioProcessor != null) {
            audioProcessor.stopAll();
            Log.i(TAG, "已停止所有音频活动");
            
            // 添加事件消息
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "已停止所有音频活动")
            );
        }
    }

    /**
     * 处理文本消息
     */
    private void handleTextMessage(String message) {
        try {
            JSONObject jsonData = new JSONObject(message);
            String type = jsonData.optString("type", "");
            
            // 根据消息类型处理
            switch (type) {
                case "tts":
                    // TTS消息处理
                    String state = jsonData.optString("state", "");
                    switch (state){
                        case "start":
                            if (deviceState.getDeviceState() == DeviceStateManager.DeviceState.IDLE || deviceState.getDeviceState() == DeviceStateManager.DeviceState.LISTENING) {
                                deviceState.setDeviceState(DeviceStateManager.DeviceState.SPEAKING);
//                                EMotionPlayer.playEMotionByDescription("微笑");
                            }
                            break;
                        case "stop":
                            if (deviceState.getDeviceState() == DeviceStateManager.DeviceState.SPEAKING) {
                                mainHandler.postDelayed(()->{
                                    // 通知状态变化，由音频线程自动处理停止播放和后续状态
                                    if (deviceState.isKeepListening() && deviceState.getDeviceState() == DeviceStateManager.DeviceState.SPEAKING) {
                                        deviceState.setDeviceState(DeviceStateManager.DeviceState.LISTENING);
                                        protocol.sendStartListening(Protocol.ListeningMode.AUTO_STOP);
                                    } else {
                                        deviceState.setDeviceState(DeviceStateManager.DeviceState.IDLE);
                                        EMotionPlayer.playEMotionByDescription("待机");
                                    }
                                }, 800);
                            }
                            break;
                        case "sentence_start":
                            String text = jsonData.optString("text", "");
                            if (!text.isEmpty()) {
                                Log.d(TAG, "收到TTS句子: " + text);
                                dispatchEvent(EventType.TEXT_MESSAGE, text);
                            }
                            break;
                        case "sentence_end":
                            break;
                        default:
                            deviceState.setDeviceState(DeviceStateManager.DeviceState.IDLE);
                            EMotionPlayer.playEMotionByDescription("待机");
                    }
                    break;
                    
                case "stt":
                    // 处理语音识别结果
                    String sttText = jsonData.optString("text", "");
                    if (!sttText.isEmpty()) {
                        Log.d(TAG, "收到语音识别结果: " + sttText);
                        dispatchEvent(EventType.USER_MESSAGE, sttText);
                    }
                    break;

                case "llm":
                    // 处理情绪表情
                    //  {"type":"llm","text":"😊","emotion":"happy","session_id":"74babcf3"}
                    String llmText = jsonData.optString("emotion", "");
                    if (!llmText.isEmpty()) {
                        Log.d(TAG, "收到情绪表情结果: " + llmText);
                        // 播放表情到矩阵灯
                        EMotionPlayer.playEMotionByServerCode(llmText);
                    }
                    break;

                case "iot":
                    JSONArray commands = jsonData.optJSONArray("commands");
                    if (commands != null) {
                        Log.d(TAG, "收到iot命令: " + commands);
                        for (int i = 0; i < commands.length(); i++) {
                            JSONObject command = commands.getJSONObject(i);
                            ThingManager.getInstance().invoke(command);
                        }
                    }
                    break;

                case "goodbye":
                    deviceState.setKeepListening(false);
                    EMotionPlayer.playEMotionByDescription("待机");
                    break;

                default:
                    // 对于其他类型的消息，直接忽略或全部分发
                    dispatchEvent(EventType.TEXT_MESSAGE, message);
                    break;
            }
            
        } catch (JSONException e) {
            Log.e(TAG, "解析JSON消息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "语音通信服务",
                    NotificationManager.IMPORTANCE_LOW);
            channel.setDescription("提供语音通信功能的服务通知");
            
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    /**
     * 创建前台服务通知
     */
    private Notification createNotification() {
        String contentText = "语音通信服务正在运行中";

        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE);

        return buildNotification(contentText);
    }

    private Notification buildNotification(String content) {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE);

        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("小萝卜语音助手")
                .setContentText(content)
                .setSmallIcon(R.drawable.ic_notifications_black)
                .setContentIntent(pendingIntent)
                .build();
    }

    private void updateNotification(String content) {
        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        if (manager != null) {
            manager.notify(NOTIFICATION_ID, buildNotification(content));
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "语音通信服务销毁");

        instance = null;
        
        // 注销广播接收器
        try {
            unregisterReceiver(broadcastReceiver);
            Log.d(TAG, "唤醒广播接收器已注销");
        } catch (Exception e) {
            Log.e(TAG, "注销广播接收器失败: " + e.getMessage(), e);
        }
        
        // 释放资源
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
        }
        
        // 断开连接
        disconnect();
        
        // 释放音频资源
        if (audioProcessor != null) {
            audioProcessor.release();
        }

        // 清理监听器
        listeners.clear();
        
        // 更新服务状态
        serviceRunning.set(false);

        // 添加事件消息
        GlobalMessageManager.getInstance().addMessage(
            new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "语音交互服务已停止")
        );
    }
    
    /**
     * 判断是否已连接
     */
    public boolean isConnected() {
        return protocol != null && protocol.isConnected();
    }

    /**
     * 协议监听器
     */
    private class ServiceProtocolListener implements ProtocolListener {
        @Override
        public void onConnected() {
            Log.i(TAG, "已连接到服务器");
            dispatchEvent(EventType.CONNECTED, null);
            if (fromWakeupCommand) {
                protocol.openAudioChannel();
            }
        }
        
        @Override
        public void onDisconnected() {
            Log.i(TAG, "与服务器断开连接");
            dispatchEvent(EventType.DISCONNECTED, null);
        }
        
        @Override
        public void onJsonMessage(JSONObject jsonMessage) {
            try {
                Log.d(TAG, "收到JSON消息: " + jsonMessage.toString());
                handleTextMessage(jsonMessage.toString());
            } catch (Exception e) {
                Log.e(TAG, "处理JSON消息出错: " + e.getMessage(), e);
            }
        }
        
        @Override
        public void onTextMessage(String textMessage) {
            Log.d(TAG, "收到文本消息: " + textMessage);
            handleTextMessage(textMessage);
        }
        
        @Override
        public void onBinaryMessage(byte[] binaryData) {
            Log.v(TAG, "收到二进制消息，大小: " + binaryData.length + " 字节");
            dispatchEvent(EventType.AUDIO_MESSAGE, binaryData);
        }
        
        @Override
        public void onAudioChannelOpened() {
            Log.i(TAG, "音频通道已打开");
            
            // 添加事件消息
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "音频通道已打开")
            );

            // 发送 iot 配置&状态
            protocol.sendIotDescriptors(ThingManager.getInstance().getDescriptorsJson());
            protocol.sendIotStates(ThingManager.getInstance().getStatesJson(false).getJson());

        }
        
        @Override
        public void onAudioChannelClosed() {
            deviceState.setDeviceState(DeviceStateManager.DeviceState.IDLE);

            Log.i(TAG, "音频通道已关闭，停止监听");
            
            // 添加事件消息
            GlobalMessageManager.getInstance().addMessage(
                new MessageAdapter.Message(MessageAdapter.Message.TYPE_EVENT, "音频通道已关闭")
            );

            // 停止所有音频活动
            audioProcessor.stopAll();
        }
        
        @Override
        public void onAudioReceived(byte[] audioData, int sampleRate) {
            deviceState.setDeviceState(DeviceStateManager.DeviceState.SPEAKING);

//            Log.d(TAG, "收到音频数据，大小: " + audioData.length + " 字节" + ", sampleRate: "+ sampleRate);

            // 设置服务器返回音频采样率
            audioProcessor.setPlayerSampleRate(sampleRate);

            // 添加到音频队列 - 在AudioProcessor中处理
            audioProcessor.addDecodeQueue(audioData);
        }
        
        @Override
        public void onError(String errorMsg) {
            Log.e(TAG, "协议错误: " + errorMsg);
            dispatchEvent(EventType.ERROR, errorMsg);
        }
    }

    /**
     * 中断当前通话（语音唤醒时使用）
     */
    public void sendAbortMessage(String wakeWork) {
        if (!isConnected()) {
            return;
        }

        try {
            protocol.sendAbortSpeaking(Protocol.AbortReason.WAKE_WORD_DETECTED);
            if (!wakeWork.isEmpty()) {
                protocol.sendWakeWordDetected(wakeWork);
            }
        } catch (Exception e) {
            Log.e(TAG, "发送中断消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理唤醒后的操作（内部方法）
     */
    private void processWakeup() {
        deviceState.setKeepListening(true);
        // 如果已经在监听语音，则发送中断消息
        if (deviceState.getDeviceState() == DeviceStateManager.DeviceState.SPEAKING) {
            deviceState.setDeviceState(DeviceStateManager.DeviceState.IDLE);
            sendAbortMessage("");
        } else {
            protocol.sendStartListening(Protocol.ListeningMode.ALWAYS_ON);
            deviceState.setDeviceState(DeviceStateManager.DeviceState.LISTENING);
        }
    }

    /**
     * 打开音频通道并处理唤醒
     */
    private void waitAudioChannelOpened() {
        // 使用Handler循环检查通道是否已打开，最多等待10秒
        final int MAX_ATTEMPTS = 100; // 最大尝试次数
        final int CHECK_INTERVAL = 100; // 检查间隔(毫秒)
        final Handler handler = new Handler(Looper.getMainLooper());
        final int[] attempts = {0};

        EMotionPlayer.playEMotionByDescription("等待");
        final Runnable checkChannelRunnable = new Runnable() {
            @Override
            public void run() {
                attempts[0]++;
                if (protocol.isAudioChannelOpened()) {
                    Log.i(TAG, "音频通道已成功打开，继续处理唤醒");
                    processWakeup();
                } else if (attempts[0] < MAX_ATTEMPTS) {
                    Log.d(TAG, "等待音频通道打开，尝试次数：" + attempts[0]);
                    handler.postDelayed(this, CHECK_INTERVAL);
                } else {
                    Log.e(TAG, "等待音频通道打开超时");
                    EMotionPlayer.playEMotionByDescription("待机");
                }
            }
        };
        handler.post(checkChannelRunnable);
    }

    /**
     * 创建广播接收器
     */
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private void registerExitChatReceiver() {
        IntentFilter filter = new IntentFilter(ACTION_EXIT_CHAT);

        // 使用兼容不同Android版本的方式注册广播接收器
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            // Android 13 (API 33)及以上需要指定RECEIVER_NOT_EXPORTED
            registerReceiver(broadcastReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            // 较低版本的Android
            registerReceiver(broadcastReceiver, filter);
        }

        Log.d(TAG, "退出对话广播接收器已注册");
    }

    /**
     * 创建广播接收器
     */
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private void registerWakeupReceiver() {
        IntentFilter filter = new IntentFilter(ACTION_WAKEUP);

        // 使用兼容不同Android版本的方式注册广播接收器
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            // Android 13 (API 33)及以上需要指定RECEIVER_NOT_EXPORTED
            registerReceiver(broadcastReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            // 较低版本的Android
            registerReceiver(broadcastReceiver, filter);
        }

        Log.d(TAG, "唤醒广播接收器已注册");
    }

    /**
     * 唤醒广播接收器
     */
    private final BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            switch (intent.getAction()) {
                case ACTION_WAKEUP:
                    Log.i(TAG, "收到唤醒广播");
                    // 检查音频通道是否已打开
                    if (!protocol.isAudioChannelOpened()) {
                        // 先打开音频通道
                        Log.i(TAG, "音频通道未打开，正在打开音频通道");
                        protocol.openAudioChannel();
                        waitAudioChannelOpened();
                    } else {
                        // 音频通道已打开，直接处理
                        Log.i(TAG, "音频通道已打开，直接处理唤醒");
                        processWakeupInternal();
                    }
                    break;
                case ACTION_EXIT_CHAT:
                    Log.i(TAG, "收到退出对话广播");
                    protocol.sendStopSpeaking();
//                    protocol.closeAudioChannel();
                    break;
            }
        }

        /**
         * 处理唤醒后的操作
         */
        private void processWakeupInternal() {
            processWakeup();
        }

    };

}