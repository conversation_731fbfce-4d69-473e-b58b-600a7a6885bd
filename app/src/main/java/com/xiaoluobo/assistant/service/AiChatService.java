package com.xiaoluobo.assistant.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.xiaoluobo.assistant.R;
import com.xiaoluobo.assistant.audio.AudioManager;
import com.xiaoluobo.assistant.audio.AudioDecodeQueue;
import com.xiaoluobo.assistant.audio.OpusMediaPlayer;
import com.xiaoluobo.assistant.iot.Method;
import com.xiaoluobo.assistant.iot.Parameter;
import com.xiaoluobo.assistant.iot.ThingManager;
import com.xiaoluobo.assistant.iot.things.LocalMediaPlayer;
import com.xiaoluobo.assistant.protocols.Protocol;
import com.xiaoluobo.assistant.protocols.ProtocolFactory;
import com.xiaoluobo.assistant.protocols.ProtocolListener;
import com.xiaoluobo.assistant.utils.DeviceStateManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 优化的AI聊天服务
 * 复用现有的protocols协议，正确处理opus音频
 * 与LocalMediaPlayer智能集成
 */
public class AiChatService extends Service implements ProtocolListener {
    
    private static final String TAG = "AiChatService";
    private static final String CHANNEL_ID = "AiChatChannel";
    private static final int NOTIFICATION_ID = 4;
    
    // 服务状态
    private static final AtomicBoolean serviceRunning = new AtomicBoolean(false);
    private static AiChatService instance;
    
    // 核心组件
    private Protocol protocol;
    private AudioManager audioManager;
    private AudioDecodeQueue audioDecodeQueue;
    private OpusMediaPlayer opusMediaPlayer;
    private DeviceStateManager deviceState;
    private LocalMediaPlayer mediaPlayer;
    
    // AI聊天状态
    private final AtomicBoolean isChatActive = new AtomicBoolean(false);
    private final AtomicBoolean isAiSpeaking = new AtomicBoolean(false);
    private final AtomicBoolean isListening = new AtomicBoolean(false);
    private final AtomicReference<String> currentSessionId = new AtomicReference<>();
    
    // 主线程Handler
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // AI聊天监听器接口
    public interface AiChatListener {
        void onChatStarted();
        void onChatEnded();
        void onAiSpeakingStarted();
        void onAiSpeakingEnded();
        void onListeningStarted();
        void onListeningEnded();
        void onError(String error);
    }
    
    private AiChatListener chatListener;
    
    /**
     * 获取服务实例
     */
    public static AiChatService getInstance() {
        return instance;
    }
    
    /**
     * 检查服务是否运行
     */
    public static boolean isRunning() {
        return serviceRunning.get();
    }
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "AI聊天服务创建");
        
        instance = this;
        serviceRunning.set(true);
        
        // 初始化核心组件
        initializeComponents();
        
        // 创建通知渠道
        createNotificationChannel();
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, buildNotification("AI聊天服务已启动"));
        
        Log.i(TAG, "AI聊天服务创建完成");
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "AI聊天服务启动命令");
        return START_STICKY; // 服务被杀死后自动重启
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // 不支持绑定
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "AI聊天服务销毁");
        
        instance = null;
        serviceRunning.set(false);
        
        // 清理资源
        cleanup();
    }
    
    /**
     * 初始化核心组件
     */
    private void initializeComponents() {
        // 初始化设备状态管理器
        deviceState = DeviceStateManager.getInstance();
        
        // 初始化音频组件
        audioManager = AudioManager.getInstance();
        audioManager.setContext(this);
        audioDecodeQueue = new AudioDecodeQueue();

        // 初始化Opus媒体播放器
        opusMediaPlayer = new OpusMediaPlayer(this);
        opusMediaPlayer.setPlaybackListener(new OpusMediaPlayer.PlaybackListener() {
            @Override
            public void onPlaybackStarted() {
                Log.i(TAG, "AI语音播放开始");
                // 确保AI说话状态正确
                if (!isAiSpeaking.get()) {
                    startAiSpeaking();
                }
            }

            @Override
            public void onPlaybackCompleted() {
                Log.i(TAG, "AI语音播放完成");
                // AI说话结束
                stopAiSpeaking();
            }

            @Override
            public void onPlaybackError(String error) {
                Log.e(TAG, "AI语音播放错误: " + error);
                // 发生错误时也要结束AI说话状态
                stopAiSpeaking();
            }
        });
        
        // 获取LocalMediaPlayer实例
        ThingManager thingManager = ThingManager.getInstance();
        mediaPlayer = (LocalMediaPlayer) thingManager.getThing("LocalMediaPlayer");
        
        // 初始化协议
        initializeProtocol();
        
        Log.i(TAG, "核心组件初始化完成");
    }
    
    /**
     * 初始化协议
     */
    private void initializeProtocol() {
        try {
            // 使用ProtocolFactory创建协议实例
            protocol = ProtocolFactory.createProtocol(this, ProtocolFactory.ProtocolType.MQTT);

            if (protocol != null) {
                protocol.addListener(this);
                Log.i(TAG, "协议初始化成功: " + protocol.getClass().getSimpleName());
            } else {
                Log.e(TAG, "协议初始化失败");
            }
        } catch (Exception e) {
            Log.e(TAG, "协议初始化异常", e);
        }
    }
    
    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "AI聊天服务",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("AI语音聊天服务通知");
            channel.setShowBadge(false);
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    /**
     * 构建通知
     */
    private Notification buildNotification(String content) {
        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("小萝卜AI助手")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build();
    }
    
    /**
     * 更新通知内容
     */
    private void updateNotification(String content) {
        NotificationManager manager = getSystemService(NotificationManager.class);
        if (manager != null) {
            manager.notify(NOTIFICATION_ID, buildNotification(content));
        }
    }
    
    // ==================== 公共API ====================
    
    /**
     * 设置AI聊天监听器
     */
    public void setAiChatListener(AiChatListener listener) {
        this.chatListener = listener;
    }
    
    /**
     * 开始AI聊天
     */
    public void startChat() {
        if (isChatActive.get()) {
            Log.w(TAG, "AI聊天已经活跃，忽略重复启动");
            return;
        }
        
        Log.i(TAG, "开始AI聊天");
        isChatActive.set(true);
        
        // 通知媒体播放器AI聊天开始
        if (mediaPlayer != null) {
            try {
                Method method = mediaPlayer.getMethods().getMethod("onAiChatStart");
                if (method != null) {
                    method.invoke(new Parameter.ParameterList());
                }
            } catch (Exception e) {
                Log.e(TAG, "通知媒体播放器AI聊天开始失败", e);
            }
        }
        
        // 连接到服务器
        connectToServer();
        
        // 更新通知
        updateNotification("AI聊天活跃中");
        
        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onChatStarted());
        }
    }
    
    /**
     * 结束AI聊天
     */
    public void endChat() {
        if (!isChatActive.get()) {
            Log.w(TAG, "AI聊天未活跃，忽略结束请求");
            return;
        }
        
        Log.i(TAG, "结束AI聊天");
        
        // 停止所有活动
        stopListening();
        stopAiSpeaking();
        
        isChatActive.set(false);
        currentSessionId.set(null);
        
        // 断开连接
        disconnectFromServer();
        
        // 通知媒体播放器AI聊天结束
        if (mediaPlayer != null) {
            try {
                Method method = mediaPlayer.getMethods().getMethod("onAiChatEnd");
                if (method != null) {
                    method.invoke(new Parameter.ParameterList());
                }
            } catch (Exception e) {
                Log.e(TAG, "通知媒体播放器AI聊天结束失败", e);
            }
        }
        
        // 更新通知
        updateNotification("AI聊天服务待机中");
        
        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onChatEnded());
        }
    }
    
    /**
     * 开始监听用户语音
     */
    public void startListening() {
        if (!isChatActive.get()) {
            Log.w(TAG, "AI聊天未活跃，无法开始监听");
            return;
        }
        
        if (isListening.get()) {
            Log.w(TAG, "已在监听中，忽略重复请求");
            return;
        }
        
        Log.i(TAG, "开始监听用户语音");
        isListening.set(true);
        
        // 开始录音
        startRecording();
        
        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onListeningStarted());
        }
    }
    
    /**
     * 停止监听用户语音
     */
    public void stopListening() {
        if (!isListening.get()) {
            return;
        }
        
        Log.i(TAG, "停止监听用户语音");
        isListening.set(false);
        
        // 停止录音
        stopRecording();
        
        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onListeningEnded());
        }
    }
    
    /**
     * 获取当前状态
     */
    public boolean isChatActive() {
        return isChatActive.get();
    }
    
    public boolean isAiSpeaking() {
        return isAiSpeaking.get();
    }
    
    public boolean isListening() {
        return isListening.get();
    }

    // ==================== 协议监听器实现 ====================

    @Override
    public void onConnected() {
        Log.i(TAG, "已连接到AI服务器");
        mainHandler.post(() -> updateNotification("已连接到AI服务器"));
    }

    @Override
    public void onDisconnected() {
        Log.i(TAG, "与AI服务器断开连接");
        mainHandler.post(() -> {
            updateNotification("与AI服务器断开连接");
            // 如果聊天活跃中，尝试重连
            if (isChatActive.get()) {
                reconnectToServer();
            }
        });
    }

    @Override
    public void onJsonMessage(JSONObject jsonMessage) {
        Log.d(TAG, "收到JSON消息: " + jsonMessage.toString());

        try {
            String type = jsonMessage.optString("type");

            switch (type) {
                case "session_start":
                    handleSessionStart(jsonMessage);
                    break;
                case "session_end":
                    handleSessionEnd(jsonMessage);
                    break;
                case "ai_response_start":
                    handleAiResponseStart(jsonMessage);
                    break;
                case "ai_response_end":
                    handleAiResponseEnd(jsonMessage);
                    break;
                case "media_command":
                    handleMediaCommand(jsonMessage);
                    break;
                case "error":
                    handleError(jsonMessage);
                    break;
                default:
                    Log.w(TAG, "未知的JSON消息类型: " + type);
            }
        } catch (Exception e) {
            Log.e(TAG, "处理JSON消息异常", e);
        }
    }

    @Override
    public void onTextMessage(String textMessage) {
        Log.d(TAG, "收到文本消息: " + textMessage);
    }

    @Override
    public void onBinaryMessage(byte[] binaryData) {
        Log.d(TAG, "收到二进制消息，长度: " + binaryData.length);
    }

    @Override
    public void onAudioChannelOpened() {
        Log.i(TAG, "音频通道已开启");
    }

    @Override
    public void onAudioChannelClosed() {
        Log.i(TAG, "音频通道已关闭");
        // 如果AI正在说话，标记为结束
        if (isAiSpeaking.get()) {
            stopAiSpeaking();
        }
    }

    @Override
    public void onAudioReceived(byte[] audioData, int sampleRate) {
        // 收到opus编码的音频数据，直接发送给OpusMediaPlayer处理
        if (audioData != null && audioData.length > 0) {
            Log.d(TAG, "收到AI语音数据: " + audioData.length + " bytes, 采样率: " + sampleRate);

            // 将opus数据发送给OpusMediaPlayer
            opusMediaPlayer.addOpusAudioData(audioData);

            // OpusMediaPlayer会在开始播放时通过监听器通知我们
            // 这样可以确保AI说话状态与实际播放状态同步
        }
    }

    @Override
    public void onError(String errorMsg) {
        Log.e(TAG, "协议错误: " + errorMsg);

        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onError(errorMsg));
        }

        // 更新通知显示错误
        updateNotification("AI服务错误: " + errorMsg);
    }

    // ==================== 消息处理方法 ====================

    /**
     * 处理会话开始
     */
    private void handleSessionStart(JSONObject message) {
        String sessionId = message.optString("session_id");
        currentSessionId.set(sessionId);
        Log.i(TAG, "AI会话开始: " + sessionId);
    }

    /**
     * 处理会话结束
     */
    private void handleSessionEnd(JSONObject message) {
        String sessionId = message.optString("session_id");
        Log.i(TAG, "AI会话结束: " + sessionId);

        // 结束聊天
        mainHandler.post(this::endChat);
    }

    /**
     * 处理AI回复开始
     */
    private void handleAiResponseStart(JSONObject message) {
        Log.i(TAG, "AI开始回复");
        startAiSpeaking();
    }

    /**
     * 处理AI回复结束
     */
    private void handleAiResponseEnd(JSONObject message) {
        Log.i(TAG, "AI回复结束");
        stopAiSpeaking();
    }

    /**
     * 处理媒体控制命令
     */
    private void handleMediaCommand(JSONObject message) {
        if (mediaPlayer == null) {
            Log.w(TAG, "媒体播放器未初始化，无法处理媒体命令");
            return;
        }

        try {
            String command = message.getString("command");
            JSONObject params = message.optJSONObject("params");

            Log.i(TAG, "收到媒体命令: " + command);

            // 通过IoT框架调用媒体播放器方法
            switch (command) {
                case "findAndPlay":
                    String content = params != null ? params.optString("content") : "";
                    invokeMediaPlayerMethod("findAndPlay", "content", content);
                    break;
                case "playBedtimeStories":
                    invokeMediaPlayerMethod("playBedtimeStories", null, null);
                    break;
                case "playSongs":
                    invokeMediaPlayerMethod("playSongs", null, null);
                    break;
                case "pauseStory":
                    invokeMediaPlayerMethod("pauseStory", null, null);
                    break;
                case "continueStory":
                    invokeMediaPlayerMethod("continueStory", null, null);
                    break;
                case "nextStory":
                    invokeMediaPlayerMethod("nextStory", null, null);
                    break;
                case "previousStory":
                    invokeMediaPlayerMethod("previousStory", null, null);
                    break;
                default:
                    Log.w(TAG, "未知的媒体命令: " + command);
            }
        } catch (JSONException e) {
            Log.e(TAG, "解析媒体命令失败", e);
        }
    }

    /**
     * 处理错误消息
     */
    private void handleError(JSONObject message) {
        String error = message.optString("error", "未知错误");
        Log.e(TAG, "服务器错误: " + error);

        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onError(error));
        }
    }

    // ==================== 音频处理方法 ====================

    /**
     * 开始AI说话
     */
    private void startAiSpeaking() {
        if (isAiSpeaking.get()) {
            return;
        }

        Log.i(TAG, "AI开始说话");
        isAiSpeaking.set(true);

        // 通知媒体播放器AI开始说话
        if (mediaPlayer != null) {
            try {
                mediaPlayer.getMethods().invokeMethod("onAiSpeakStart", null);
            } catch (Exception e) {
                Log.e(TAG, "通知媒体播放器AI开始说话失败", e);
            }
        }

        // 开始播放音频队列
        startAudioPlayback();

        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onAiSpeakingStarted());
        }

        updateNotification("AI正在回复");
    }

    /**
     * 停止AI说话
     */
    private void stopAiSpeaking() {
        if (!isAiSpeaking.get()) {
            return;
        }

        Log.i(TAG, "AI停止说话");
        isAiSpeaking.set(false);

        // 停止音频播放
        stopAudioPlayback();

        // 通知媒体播放器AI停止说话
        if (mediaPlayer != null) {
            try {
                mediaPlayer.getMethods().invokeMethod("onAiSpeakEnd", null);
            } catch (Exception e) {
                Log.e(TAG, "通知媒体播放器AI停止说话失败", e);
            }
        }

        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onAiSpeakingEnded());
        }

        updateNotification("AI聊天活跃中");
    }

    /**
     * 开始录音
     */
    private void startRecording() {
        if (audioManager != null) {
            audioManager.startRecording();
            Log.i(TAG, "开始录音");
        }
    }

    /**
     * 停止录音
     */
    private void stopRecording() {
        if (audioManager != null) {
            audioManager.stopRecording();
            Log.i(TAG, "停止录音");
        }
    }

    /**
     * 开始音频播放
     */
    private void startAudioPlayback() {
        // OpusMediaPlayer会自动开始播放，这里不需要手动控制
        Log.i(TAG, "AI语音播放由OpusMediaPlayer自动管理");
    }

    /**
     * 停止音频播放
     */
    private void stopAudioPlayback() {
        if (opusMediaPlayer != null) {
            opusMediaPlayer.stop();
            Log.i(TAG, "停止AI语音播放");
        }
    }

    // ==================== 连接管理方法 ====================

    /**
     * 连接到服务器
     */
    private void connectToServer() {
        if (protocol != null) {
            try {
                protocol.connect();
                Log.i(TAG, "正在连接到AI服务器");
            } catch (Exception e) {
                Log.e(TAG, "连接AI服务器失败", e);
                if (chatListener != null) {
                    mainHandler.post(() -> chatListener.onError("连接失败: " + e.getMessage()));
                }
            }
        }
    }

    /**
     * 断开服务器连接
     */
    private void disconnectFromServer() {
        if (protocol != null) {
            try {
                protocol.disconnect();
                Log.i(TAG, "断开AI服务器连接");
            } catch (Exception e) {
                Log.e(TAG, "断开连接失败", e);
            }
        }
    }

    /**
     * 重连到服务器
     */
    private void reconnectToServer() {
        Log.i(TAG, "尝试重连AI服务器");

        // 延迟重连，避免频繁重连
        mainHandler.postDelayed(() -> {
            if (isChatActive.get()) {
                connectToServer();
            }
        }, 3000);
    }

    // ==================== 辅助方法 ====================

    /**
     * 调用媒体播放器方法
     */
    private void invokeMediaPlayerMethod(String methodName, String paramName, String paramValue) {
        if (mediaPlayer == null) {
            Log.w(TAG, "媒体播放器未初始化");
            return;
        }

        try {
            if (paramName != null && paramValue != null) {
                // 有参数的方法调用
                // 这里需要根据IoT框架的具体实现来调用方法
                // 暂时使用反射或直接调用的方式
                Log.i(TAG, "调用媒体播放器方法: " + methodName + "(" + paramName + "=" + paramValue + ")");
            } else {
                // 无参数的方法调用
                Log.i(TAG, "调用媒体播放器方法: " + methodName + "()");
            }

            // TODO: 实际的方法调用需要根据IoT框架的API来实现

        } catch (Exception e) {
            Log.e(TAG, "调用媒体播放器方法失败: " + methodName, e);
        }
    }

    /**
     * 发送消息到服务器
     */
    private void sendMessageToServer(JSONObject message) {
        if (protocol != null) {
            try {
                protocol.sendMessage(message.toString());
                Log.d(TAG, "发送消息到服务器: " + message.toString());
            } catch (Exception e) {
                Log.e(TAG, "发送消息失败", e);
            }
        }
    }

    /**
     * 发送音频数据到服务器
     */
    private void sendAudioToServer(byte[] audioData) {
        if (protocol != null && audioData != null) {
            try {
                protocol.sendAudio(audioData);
                Log.d(TAG, "发送音频数据到服务器，长度: " + audioData.length);
            } catch (Exception e) {
                Log.e(TAG, "发送音频数据失败", e);
            }
        }
    }

    // ==================== 清理方法 ====================

    /**
     * 清理资源
     */
    private void cleanup() {
        Log.i(TAG, "开始清理资源");

        // 停止所有活动
        stopListening();
        stopAiSpeaking();

        // 断开连接
        disconnectFromServer();

        // 清理协议
        if (protocol != null) {
            protocol.removeListener(this);
            protocol = null;
        }

        // 清理音频组件
        if (audioManager != null) {
            audioManager.cleanup();
        }

        if (audioDecodeQueue != null) {
            audioDecodeQueue.clear();
        }

        // 清理Opus媒体播放器
        if (opusMediaPlayer != null) {
            opusMediaPlayer.release();
            opusMediaPlayer = null;
        }

        // 清理状态
        isChatActive.set(false);
        isAiSpeaking.set(false);
        isListening.set(false);
        currentSessionId.set(null);

        // 清理监听器
        chatListener = null;

        Log.i(TAG, "资源清理完成");
    }
}
