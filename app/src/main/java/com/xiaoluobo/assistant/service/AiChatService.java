package com.xiaoluobo.assistant.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.xiaoluobo.assistant.R;
import com.xiaoluobo.assistant.audio.AudioManager;
import com.xiaoluobo.assistant.audio.AudioDecodeQueue;
import com.xiaoluobo.assistant.iot.ThingManager;
import com.xiaoluobo.assistant.iot.things.LocalMediaPlayer;
import com.xiaoluobo.assistant.protocols.Protocol;
import com.xiaoluobo.assistant.protocols.ProtocolFactory;
import com.xiaoluobo.assistant.protocols.ProtocolListener;
import com.xiaoluobo.assistant.utils.DeviceStateManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 优化的AI聊天服务
 * 复用现有的protocols协议，正确处理opus音频
 * 与LocalMediaPlayer智能集成
 */
public class AiChatService extends Service implements ProtocolListener {
    
    private static final String TAG = "AiChatService";
    private static final String CHANNEL_ID = "AiChatChannel";
    private static final int NOTIFICATION_ID = 4;
    
    // 服务状态
    private static final AtomicBoolean serviceRunning = new AtomicBoolean(false);
    private static AiChatService instance;
    
    // 核心组件
    private Protocol protocol;
    private AudioManager audioManager;
    private AudioDecodeQueue audioDecodeQueue;
    private DeviceStateManager deviceState;
    private LocalMediaPlayer mediaPlayer;
    
    // AI聊天状态
    private final AtomicBoolean isChatActive = new AtomicBoolean(false);
    private final AtomicBoolean isAiSpeaking = new AtomicBoolean(false);
    private final AtomicBoolean isListening = new AtomicBoolean(false);
    private final AtomicReference<String> currentSessionId = new AtomicReference<>();
    
    // 主线程Handler
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // AI聊天监听器接口
    public interface AiChatListener {
        void onChatStarted();
        void onChatEnded();
        void onAiSpeakingStarted();
        void onAiSpeakingEnded();
        void onListeningStarted();
        void onListeningEnded();
        void onError(String error);
    }
    
    private AiChatListener chatListener;
    
    /**
     * 获取服务实例
     */
    public static AiChatService getInstance() {
        return instance;
    }
    
    /**
     * 检查服务是否运行
     */
    public static boolean isRunning() {
        return serviceRunning.get();
    }
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "AI聊天服务创建");
        
        instance = this;
        serviceRunning.set(true);
        
        // 初始化核心组件
        initializeComponents();
        
        // 创建通知渠道
        createNotificationChannel();
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, buildNotification("AI聊天服务已启动"));
        
        Log.i(TAG, "AI聊天服务创建完成");
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "AI聊天服务启动命令");
        return START_STICKY; // 服务被杀死后自动重启
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // 不支持绑定
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "AI聊天服务销毁");
        
        instance = null;
        serviceRunning.set(false);
        
        // 清理资源
        cleanup();
    }
    
    /**
     * 初始化核心组件
     */
    private void initializeComponents() {
        // 初始化设备状态管理器
        deviceState = DeviceStateManager.getInstance();
        
        // 初始化音频组件
        audioManager = AudioManager.getInstance();
        audioManager.setContext(this);
        audioDecodeQueue = new AudioDecodeQueue();
        
        // 获取LocalMediaPlayer实例
        ThingManager thingManager = ThingManager.getInstance();
        mediaPlayer = (LocalMediaPlayer) thingManager.getThing("LocalMediaPlayer");
        
        // 初始化协议
        initializeProtocol();
        
        Log.i(TAG, "核心组件初始化完成");
    }
    
    /**
     * 初始化协议
     */
    private void initializeProtocol() {
        try {
            // 使用ProtocolFactory创建协议实例
            String deviceId = deviceState.getDeviceId();
            protocol = ProtocolFactory.createProtocol("mqtt", deviceId);
            
            if (protocol != null) {
                protocol.addListener(this);
                Log.i(TAG, "协议初始化成功: " + protocol.getClass().getSimpleName());
            } else {
                Log.e(TAG, "协议初始化失败");
            }
        } catch (Exception e) {
            Log.e(TAG, "协议初始化异常", e);
        }
    }
    
    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "AI聊天服务",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("AI语音聊天服务通知");
            channel.setShowBadge(false);
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    /**
     * 构建通知
     */
    private Notification buildNotification(String content) {
        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("小萝卜AI助手")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build();
    }
    
    /**
     * 更新通知内容
     */
    private void updateNotification(String content) {
        NotificationManager manager = getSystemService(NotificationManager.class);
        if (manager != null) {
            manager.notify(NOTIFICATION_ID, buildNotification(content));
        }
    }
    
    // ==================== 公共API ====================
    
    /**
     * 设置AI聊天监听器
     */
    public void setAiChatListener(AiChatListener listener) {
        this.chatListener = listener;
    }
    
    /**
     * 开始AI聊天
     */
    public void startChat() {
        if (isChatActive.get()) {
            Log.w(TAG, "AI聊天已经活跃，忽略重复启动");
            return;
        }
        
        Log.i(TAG, "开始AI聊天");
        isChatActive.set(true);
        
        // 通知媒体播放器AI聊天开始
        if (mediaPlayer != null) {
            mediaPlayer.handleAiChatStart();
        }
        
        // 连接到服务器
        connectToServer();
        
        // 更新通知
        updateNotification("AI聊天活跃中");
        
        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onChatStarted());
        }
    }
    
    /**
     * 结束AI聊天
     */
    public void endChat() {
        if (!isChatActive.get()) {
            Log.w(TAG, "AI聊天未活跃，忽略结束请求");
            return;
        }
        
        Log.i(TAG, "结束AI聊天");
        
        // 停止所有活动
        stopListening();
        stopAiSpeaking();
        
        isChatActive.set(false);
        currentSessionId.set(null);
        
        // 断开连接
        disconnectFromServer();
        
        // 通知媒体播放器AI聊天结束
        if (mediaPlayer != null) {
            mediaPlayer.handleAiChatEnd();
        }
        
        // 更新通知
        updateNotification("AI聊天服务待机中");
        
        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onChatEnded());
        }
    }
    
    /**
     * 开始监听用户语音
     */
    public void startListening() {
        if (!isChatActive.get()) {
            Log.w(TAG, "AI聊天未活跃，无法开始监听");
            return;
        }
        
        if (isListening.get()) {
            Log.w(TAG, "已在监听中，忽略重复请求");
            return;
        }
        
        Log.i(TAG, "开始监听用户语音");
        isListening.set(true);
        
        // 开始录音
        startRecording();
        
        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onListeningStarted());
        }
    }
    
    /**
     * 停止监听用户语音
     */
    public void stopListening() {
        if (!isListening.get()) {
            return;
        }
        
        Log.i(TAG, "停止监听用户语音");
        isListening.set(false);
        
        // 停止录音
        stopRecording();
        
        // 通知监听器
        if (chatListener != null) {
            mainHandler.post(() -> chatListener.onListeningEnded());
        }
    }
    
    /**
     * 获取当前状态
     */
    public boolean isChatActive() {
        return isChatActive.get();
    }
    
    public boolean isAiSpeaking() {
        return isAiSpeaking.get();
    }
    
    public boolean isListening() {
        return isListening.get();
    }
