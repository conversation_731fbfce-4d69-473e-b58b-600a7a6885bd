package com.xiaoluobo.assistant.audio;

import static androidx.core.content.ContextCompat.getSystemService;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.AudioTrack;
import android.media.MediaRecorder;
import android.media.audiofx.AcousticEchoCanceler;
import android.media.audiofx.NoiseSuppressor;
import android.os.Handler;
import android.os.Looper;
import android.Manifest;
import android.content.pm.PackageManager;
import android.util.Log;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

import com.xiaoluobo.assistant.AssistantApplication;
import com.xiaoluobo.assistant.hardware.emotion.EMotionPlayer;
import com.xiaoluobo.assistant.utils.SoundPoolManager;

import androidx.core.app.ActivityCompat;

/**
 * 音频管理器
 * 负责音频录制和播放，支持Opus格式编码和解码
 */
public class AudioManager {
    private static final String TAG = "AudioManager";

    // 音频采样配置
    private static final int SAMPLE_RATE = 16000;
    private static final int CHANNELS = 1; // 严格要求使用一个通道(单声道)
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    private static final int BYTES_PER_SAMPLE = 2; // 16位采样，即2字节
    
    // 计算音频缓冲区大小，确保足够大以容纳数据
    private static final int BUFFER_SIZE = Math.max(
            AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT) * 2,
            4096); // 确保至少有4KB的缓冲区
            
    // 音频帧配置
    private static final int INPUT_FRAME_DURATION_MS = 30; // 输入采集帧，固定为30ms
    private static final int INPUT_FRAME_SIZE_SAMPLES = SAMPLE_RATE / 1000 * INPUT_FRAME_DURATION_MS * CHANNELS;
    private static final int INPUT_FRAME_SIZE_BYTES = INPUT_FRAME_SIZE_SAMPLES * BYTES_PER_SAMPLE;
    
    // Opus编码帧配置 - 60ms
    private static final int OPUS_FRAME_DURATION_MS = 60; // 使用60ms的Opus帧，与服务端匹配
    private static final int OPUS_FRAME_SIZE_SAMPLES = SAMPLE_RATE / 1000 * OPUS_FRAME_DURATION_MS * CHANNELS;
    private static final int OPUS_FRAME_SIZE_BYTES = OPUS_FRAME_SIZE_SAMPLES * BYTES_PER_SAMPLE;

    // 播放器配置
    private static int PLAYER_SAMPLE_RATE = 24000; // 服务器返回音频采样率
    private static final int PLAYER_CHANNEL_CONFIG = AudioFormat.CHANNEL_OUT_MONO;
    private static final int PLAYER_BUFFER_SIZE =
            AudioTrack.getMinBufferSize(PLAYER_SAMPLE_RATE, PLAYER_CHANNEL_CONFIG, AUDIO_FORMAT);

    // 单例实例
    private static AudioManager instance;

    // 添加 Context 成员变量
    private Context applicationContext;
    
    // 录音相关
    private AudioRecord audioRecord;
    private final AtomicBoolean isRecording = new AtomicBoolean(false);
    private ExecutorService audioExecutor;
    private List<AudioDataListener> audioDataListeners = new ArrayList<>();
    private ByteArrayOutputStream pcmBuffer; // 用于累积PCM数据的缓冲区

    // 音频效果器
    private AcousticEchoCanceler echoCanceler;
    private NoiseSuppressor noiseSuppressor;

    // 播放相关
    private AudioTrack audioTrack;
    private final AtomicBoolean isPlaying = new AtomicBoolean(false);
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 性能和状态监控
    private long totalProcessedFrames = 0;
    private long totalProcessedBytes = 0;
    private long startTime = 0;
    private int consecutiveErrorCount = 0;
    private static final int MAX_CONSECUTIVE_ERRORS = 5; // 连续错误阈值

    private OpusJniHandler opusHandler = new OpusJniHandler();

    /**
     * 音频数据监听器接口
     */
    public interface AudioDataListener {
        void onAudioData(byte[] data);
    }

    /**
     * 私有构造函数，确保单例模式
     */
    private AudioManager() {
        // 分配足够大的缓冲区，可容纳多个编码帧
        pcmBuffer = new ByteArrayOutputStream(OPUS_FRAME_SIZE_BYTES * 3);
        Log.d(TAG, "AudioManager初始化完成，采集帧: " + INPUT_FRAME_DURATION_MS + "ms，Opus帧: " + OPUS_FRAME_DURATION_MS + "ms");
        Log.d(TAG, "缓冲区大小: " + BUFFER_SIZE + "字节，输入帧大小: " + INPUT_FRAME_SIZE_BYTES + "字节，Opus帧大小: " + OPUS_FRAME_SIZE_BYTES + "字节");

        opusHandler.init(SAMPLE_RATE, PLAYER_SAMPLE_RATE, CHANNELS, 2048); // 2048 = OPUS_APPLICATION_VOIP
    }

    /**
     * 设置应用上下文
     * @param context 应用上下文
     */
    public void setContext(Context context) {
        if (context != null) {
            this.applicationContext = context.getApplicationContext();
        }
    }
    
    /**
     * 获取应用上下文
     * @return 应用上下文，可能为null
     */
    private Context getContext() {
        return applicationContext;
    }

    /**
     * 获取AudioManager单例实例
     */
    public static synchronized AudioManager getInstance() {
        if (instance == null) {
            instance = new AudioManager();
        }
        return instance;
    }

    public void setPlayerSampleRate(int sampleRate) {
        PLAYER_SAMPLE_RATE = sampleRate;
    }

    /**
     * 初始化音频录制器
     * @return 是否成功初始化
     */
    public boolean initRecorder() {
        try {
            if (audioRecord != null) {
                releaseRecorder();
            }

            // 检查录音权限
            Context context = getContext();
            if (context != null && ActivityCompat.checkSelfPermission(context, 
                    Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "无法初始化录音器: 没有RECORD_AUDIO权限");
                return false;
            }

            // 设置音频模式为通信模式（自动启用AEC）
            android.media.AudioManager audioManager = (android.media.AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            audioManager.setMode(android.media.AudioManager.MODE_IN_COMMUNICATION);
            audioManager.setSpeakerphoneOn(true);

            audioRecord = new AudioRecord(
                    // MediaRecorder.AudioSource.MIC,
                    MediaRecorder.AudioSource.VOICE_COMMUNICATION, // 关键参数
                    SAMPLE_RATE,
                    CHANNEL_CONFIG,
                    AUDIO_FORMAT,
                    BUFFER_SIZE);

            if (audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                Log.e(TAG, "AudioRecord初始化失败");
                return false;
            }

            // 启用回声消除
            if (AcousticEchoCanceler.isAvailable()) {
                AcousticEchoCanceler echoCanceler = AcousticEchoCanceler.create(audioRecord.getAudioSessionId());
                if (echoCanceler != null) {
                    echoCanceler.setEnabled(true);
                    Log.i(TAG, "已启用回声消除");
                } else {
                    Log.w(TAG, "回声消除创建失败");
                }
            }

            // 启用噪声抑制
            if (NoiseSuppressor.isAvailable()) {
                NoiseSuppressor noiseSuppressor = NoiseSuppressor.create(audioRecord.getAudioSessionId());
                if (noiseSuppressor != null) {
                    noiseSuppressor.setEnabled(true);
                    Log.i(TAG, "已启用噪声抑制");
                } else {
                    Log.w(TAG, "噪声抑制创建失败");
                }
            }

            audioExecutor = Executors.newSingleThreadExecutor();
            Log.d(TAG, "AudioRecord初始化成功，缓冲区大小: " + BUFFER_SIZE);
            return true;
        } catch (SecurityException e) {
            Log.e(TAG, "初始化录音器失败: 权限被拒绝: " + e.getMessage(), e);
            return false;
        } catch (Exception e) {
            Log.e(TAG, "初始化录音器失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 初始化播放器
     */
    public boolean initPlayer() {
        try {
            if (audioTrack != null) {
                if (audioTrack.getState() == AudioTrack.STATE_INITIALIZED) {
                    if (audioTrack.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
                        audioTrack.play();
                        Log.d(TAG, "AudioTrack已存在但未播放，重新启动播放");
                    }
                    return true;
                }
                releasePlayer();
            }
            int bufferSize = PLAYER_BUFFER_SIZE * 6;
            audioTrack = new AudioTrack(android.media.AudioManager.STREAM_MUSIC, PLAYER_SAMPLE_RATE, AudioFormat.CHANNEL_OUT_MONO, AudioFormat.ENCODING_PCM_16BIT, bufferSize, AudioTrack.MODE_STREAM);
            if (audioTrack.getState() != AudioTrack.STATE_INITIALIZED) {
                Log.e(TAG, "AudioTrack初始化失败");
                return false;
            }
            audioTrack.play();
            isPlaying.set(true);
            Log.d(TAG, "AudioTrack初始化成功，缓冲区大小: " + bufferSize + "，采样率: " + PLAYER_SAMPLE_RATE);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "初始化播放器失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 开始录音
     */
    public void startRecording() {
        if (isRecording.get()) {
            Log.d(TAG, "录音已经在进行中");
            return;
        }

        if (audioRecord == null && !initRecorder()) {
            Log.e(TAG, "无法开始录音，录音器初始化失败");
            return;
        }

        SoundPoolManager.SoundAlias.LISTENING.play();
        EMotionPlayer.playEMotionByDescription("监听语音");

        try {
            pcmBuffer.reset(); // 清空缓冲区
            audioRecord.startRecording();
            isRecording.set(true);
            totalProcessedFrames = 0;
            totalProcessedBytes = 0;
            startTime = System.currentTimeMillis();
            consecutiveErrorCount = 0;
            
            Log.d(TAG, "开始音频录制...");
            
            audioExecutor.execute(this::processAudioData);
        } catch (Exception e) {
            Log.e(TAG, "启动录音失败: " + e.getMessage(), e);
            isRecording.set(false);
        }
    }

    /**
     * 停止录音
     */
    public void stopRecording() {
        if (!isRecording.get()) {
            return;
        }

        isRecording.set(false);
        try {
            if (audioRecord != null) {
                audioRecord.stop();
                Log.d(TAG, "停止录音");
                
                // 记录性能统计
                long duration = System.currentTimeMillis() - startTime;
                if (duration > 0 && totalProcessedFrames > 0) {
                    float framesPerSecond = totalProcessedFrames * 1000f / duration;
                    float kbps = totalProcessedBytes * 8f / duration;
                    Log.i(TAG, String.format("录音统计: %.2f帧/秒, %.2f kbps, 总处理帧: %d, 总字节: %d, 时长: %dms",
                            framesPerSecond, kbps, totalProcessedFrames, totalProcessedBytes, duration));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "停止录音时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 释放录音器资源
     */
    public void releaseRecorder() {
        stopRecording();
        
        if (audioRecord != null) {
            audioRecord.release();
            audioRecord = null;
            Log.d(TAG, "释放录音器资源");
        }
        
        if (audioExecutor != null && !audioExecutor.isShutdown()) {
            audioExecutor.shutdown();
            audioExecutor = null;
        }
    }

    /**
     * 释放播放器资源
     */
    public void releasePlayer() {
        if (audioTrack != null) {
            if (audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                try {
                    audioTrack.stop();
                } catch (IllegalStateException e) {
                    Log.w(TAG, "停止AudioTrack时出错: " + e.getMessage());
                }
            }
            audioTrack.release();
            audioTrack = null;
            Log.d(TAG, "释放播放器资源");
        }
        isPlaying.set(false);
    }

    /**
     * 释放所有资源
     */
    public void release() {
        releaseRecorder();
        releasePlayer();
        if (audioDataListeners != null) {
            audioDataListeners.clear();
        }
        Log.d(TAG, "释放所有音频资源");
    }

    /**
     * 添加音频数据监听器
     */
    public void addAudioDataListener(AudioDataListener listener) {
        if (listener != null && !audioDataListeners.contains(listener)) {
            audioDataListeners.add(listener);
        }
    }

    /**
     * 移除音频数据监听器
     */
    public void removeAudioDataListener(AudioDataListener listener) {
        if (listener != null) {
            audioDataListeners.remove(listener);
        }
    }

    /**
     * 处理采集的音频数据
     * 这个方法在单独的线程中运行，负责读取音频数据并进行编码处理
     */
    private void processAudioData() {
        // 创建用于读取数据的缓冲区
        ByteBuffer buffer = ByteBuffer.allocateDirect(INPUT_FRAME_SIZE_BYTES);
        byte[] data = new byte[INPUT_FRAME_SIZE_BYTES];
        
        // 重置PCM缓冲区
        pcmBuffer.reset();
        
        // 设置超时处理，确保即使数据不足也会在一定时间后处理
        long lastProcessTime = System.currentTimeMillis();
        final long FORCE_PROCESS_TIMEOUT_MS = 500; // 500ms强制处理一次

        while (isRecording.get() && audioRecord != null) {
            try {
                // 清空缓冲区，准备读取新数据
                buffer.clear();
                
                // 固定读取30ms的音频数据
                int bytesRead = audioRecord.read(buffer, INPUT_FRAME_SIZE_BYTES);
                
                // 检查读取是否成功
                if (bytesRead <= 0) {
                    Log.w(TAG, "读取音频数据失败: " + getAudioRecordErrorMessage(bytesRead));
                    consecutiveErrorCount++;
                    
                    // 如果连续错误过多，退出循环
                    if (consecutiveErrorCount >= MAX_CONSECUTIVE_ERRORS) {
                        Log.e(TAG, "连续读取错误达到阈值，停止录音");
                        isRecording.set(false);
                        break;
                    }
                    
                    // 短暂延时后继续尝试
                    Thread.sleep(10);
                    continue;
                }
                
                // 重置连续错误计数
                consecutiveErrorCount = 0;

                // 从缓冲区获取数据
                buffer.position(0);
                buffer.get(data, 0, bytesRead);
                
                // 添加到PCM缓冲区
                pcmBuffer.write(data, 0, bytesRead);
//                Log.d(TAG, "pcmBuffer: " + pcmBuffer.size() + " -> " + bytesRead);
                
                // 检查当前时间，判断是否需要强制处理
                long currentTime = System.currentTimeMillis();
                boolean timeoutReached = (currentTime - lastProcessTime) >= FORCE_PROCESS_TIMEOUT_MS;
                
                // 如果累积了足够的数据(60ms)或者超时，则进行Opus编码
                if (pcmBuffer.size() >= OPUS_FRAME_SIZE_BYTES || (pcmBuffer.size() > 0 && timeoutReached)) {
//                    Log.d(TAG, "处理音频数据: " + pcmBuffer.size() + " 字节" + (timeoutReached ? " (超时处理)" : ""));
                    
                    // 获取累积的PCM数据
                    byte[] pcmData = pcmBuffer.toByteArray();
                    byte[] dataToProcess;
                    
                    // 处理数据长度逻辑
                    if (pcmData.length < OPUS_FRAME_SIZE_BYTES) {
                        // 数据不足60ms，进行零填充
                        Log.d(TAG, "数据不足60ms帧，进行填充: " + pcmData.length + " -> " + OPUS_FRAME_SIZE_BYTES);
                        dataToProcess = new byte[OPUS_FRAME_SIZE_BYTES];
                        System.arraycopy(pcmData, 0, dataToProcess, 0, pcmData.length);
                        // 剩余部分保持为0，相当于静音填充
                        
                        // 处理完毕，清空缓冲区
                        pcmBuffer.reset();
                    } else {
                        // 数据超过60ms，取前60ms进行处理
                        dataToProcess = new byte[OPUS_FRAME_SIZE_BYTES];
                        System.arraycopy(pcmData, 0, dataToProcess, 0, OPUS_FRAME_SIZE_BYTES);
                        
                        // 保留剩余的数据到下一帧
                        if (pcmData.length > OPUS_FRAME_SIZE_BYTES) {
                            pcmBuffer.reset();
                            pcmBuffer.write(pcmData, OPUS_FRAME_SIZE_BYTES, pcmData.length - OPUS_FRAME_SIZE_BYTES);
                        } else {
                            pcmBuffer.reset();
                        }
                    }
                    
                    // 进行Opus编码
                    byte[] opusData = encodePcmToOpus(dataToProcess, dataToProcess.length);
                    
                    if (opusData != null) {
                        // 更新性能统计
                        totalProcessedFrames++;
                        totalProcessedBytes += opusData.length;
                        
                        // 通知所有监听器
                        for (AudioDataListener listener : new ArrayList<>(audioDataListeners)) {
                            listener.onAudioData(opusData);
                        }
                    }
                    
                    // 更新最后处理时间
                    lastProcessTime = System.currentTimeMillis();
                }
            } catch (Exception e) {
                Log.e(TAG, "处理音频数据时出错: " + e.getMessage(), e);
                
                // 短暂休眠以避免CPU占用过高
                try {
                    Thread.sleep(30);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        Log.d(TAG, "音频处理线程退出");
    }

    /**
     * 解析AudioRecord错误码
     */
    private String getAudioRecordErrorMessage(int errorCode) {
        switch (errorCode) {
            case AudioRecord.ERROR:
                return "通用错误";
            case AudioRecord.ERROR_BAD_VALUE:
                return "参数错误";
            case AudioRecord.ERROR_DEAD_OBJECT:
                return "对象不可用";
            case AudioRecord.ERROR_INVALID_OPERATION:
                return "无效操作";
            default:
                return "未知错误: " + errorCode;
        }
    }

    /**
     * 将PCM数据编码为Opus格式
     * 使用OpusJniHandler进行编码，确保帧大小为60ms
     */
    private byte[] encodePcmToOpus(byte[] pcmData, int length) {
        try {
            // 检查数据是否为空
            if (pcmData == null || length <= 0) {
                Log.e(TAG, "PCM数据为空或长度无效，无法编码");
                return null;
            }
            // 检查并确保数据长度符合要求
            if (length % BYTES_PER_SAMPLE != 0) {
                Log.w(TAG, "PCM数据长度不是采样点的整数倍，将被截断");
                length = length - (length % BYTES_PER_SAMPLE);
            }
            // 确认数据长度是否符合60ms帧大小
            if (length != OPUS_FRAME_SIZE_BYTES) {
                Log.w(TAG, "数据长度不是60ms帧大小，实际: " + length + "，预期: " + OPUS_FRAME_SIZE_BYTES);
            }
            // 如果需要，调整数据长度
            byte[] dataToEncode;
            if (length != pcmData.length) {
                dataToEncode = new byte[length];
                System.arraycopy(pcmData, 0, dataToEncode, 0, Math.min(pcmData.length, length));
            } else {
                dataToEncode = pcmData;
            }
            // 将byte[]转换为short[]（因为OpusJniHandler需要short[]输入）
            short[] pcmShorts = new short[dataToEncode.length / 2]; // 16位PCM，每个采样点2字节
            for (int i = 0; i < pcmShorts.length; i++) {
                // 小端序转换：低字节在前，高字节在后
                pcmShorts[i] = (short) ((dataToEncode[i * 2] & 0xFF) | (dataToEncode[i * 2 + 1] << 8));
            }
            // 使用OpusJniHandler进行编码
            byte[] opusData = opusHandler.encode(pcmShorts, pcmShorts.length);
            if (opusData != null) {
                final int MAX_OPUS_PACKET_SIZE = 1500;
                if (opusData.length > MAX_OPUS_PACKET_SIZE) {
                    Log.w(TAG, "Opus编码数据超过最大限制: " + opusData.length + 
                          " > " + MAX_OPUS_PACKET_SIZE + "，可能无法被服务器接受");
                }
                return opusData;
            } else {
                Log.e(TAG, "Opus编码失败，返回为空");
                return null;
            }
        } catch (Exception e) {
            Log.e(TAG, "编码PCM到Opus失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将Opus数据解码为PCM
     */
    public byte[] decodeOpusToPcm(byte[] opusData) {
        if (opusData == null || opusData.length == 0) {
            Log.e(TAG, "Opus数据为空，无法解码");
            return null;
        }
        long startTime = System.currentTimeMillis();
        try {
            // 使用OpusJniHandler进行解码
            short[] pcmShorts = opusHandler.decode(opusData, PLAYER_BUFFER_SIZE);
            if (pcmShorts == null) {
                Log.e(TAG, "Opus解码失败，返回为空");
                return null;
            }
            byte[] pcmData = new byte[pcmShorts.length * 2]; // 每个short值占用2个字节
            for (int i = 0; i < pcmShorts.length; i++) {
                pcmData[i * 2] = (byte) (pcmShorts[i] & 0xFF);
                pcmData[i * 2 + 1] = (byte) (pcmShorts[i] >> 8);
            }
            long decodingTime = System.currentTimeMillis() - startTime;
//            Log.d(TAG, String.format("Opus解码成功: %d字节 -> %d字节，耗时: %dms", opusData.length, pcmData.length, decodingTime));
            return pcmData;
        } catch (Exception e) {
            Log.e(TAG, "Opus解码异常: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 播放PCM数据
     */
    public void playPcmData(byte[] pcmData) {
        if (pcmData == null || pcmData.length == 0) {
            return;
        }
        // 确保播放器已初始化
        if (audioTrack == null || audioTrack.getState() != AudioTrack.STATE_INITIALIZED) {
            Log.d(TAG, "播放器未初始化或状态异常，尝试重新初始化");
            if (!initPlayer()) {
                Log.e(TAG, "无法播放音频，播放器初始化失败");
                return;
            }
        }

        // 确保播放器正在播放状态
        if (audioTrack.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
            Log.d(TAG, "播放器不在播放状态，尝试启动播放");
            try {
                audioTrack.play();
            } catch (IllegalStateException e) {
                Log.e(TAG, "无法启动播放: " + e.getMessage());
                // 尝试重新初始化
                if (!initPlayer()) {
                    return;
                }
            }
        }

        try {
            isPlaying.set(true);

            int bytesWritten = 0;
            int bytesToWrite = pcmData.length;
            int remainingRetries = 3; // 最多重试3次
            
            // 确保所有数据都被写入
            while (bytesWritten < bytesToWrite && remainingRetries > 0) {
                // 在API 21中使用兼容的write方法
                int result = audioTrack.write(pcmData, bytesWritten, bytesToWrite - bytesWritten);
                
                if (result > 0) {
                    bytesWritten += result;
                } else if (result == AudioTrack.ERROR_DEAD_OBJECT) {
                    // 音频设备错误，尝试重新初始化
                    Log.e(TAG, "音频设备错误，尝试重新初始化");
                    if (!initPlayer()) {
                        break;
                    }
                } else {
                    // 其他错误，减少重试次数
                    remainingRetries--;
                    Log.e(TAG, "写入音频数据失败，错误码: " + result + ", 剩余重试次数: " + remainingRetries);
                    
                    // 短暂等待后重试
                    try {
                        Thread.sleep(20);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            
            if (bytesWritten == bytesToWrite) {
                if (bytesToWrite > 5000) {
                    // 只对较大的数据包输出日志，减少日志量
                    Log.d(TAG, "音频数据写入完成: " + bytesToWrite + " 字节");
                }
            } else {
                Log.w(TAG, "音频数据写入不完整: " + bytesWritten + "/" + bytesToWrite + " 字节");
            }

        } catch (Exception e) {
            Log.e(TAG, "播放PCM数据失败: " + e.getMessage(), e);
            
            // 尝试恢复播放器
            try {
                releasePlayer();
                initPlayer();
            } catch (Exception ex) {
                Log.e(TAG, "恢复播放器失败: " + ex.getMessage());
            }
        }
    }

    /**
     * 播放Opus数据
     */
    public void playOpusData(byte[] opusData) {
        if (opusData == null || opusData.length == 0) {
            return;
        }

        // 仅在调试级别记录数据大小
//        Log.d(TAG, "解码Opus数据，大小: " + opusData.length + " 字节");

        try {
            byte[] pcmData = decodeOpusToPcm(opusData);
            if (pcmData != null) {
                playPcmData(pcmData);
            } else {
                Log.e(TAG, "无法播放，解码失败，Opus数据大小: " + opusData.length);
            }
        } catch (Exception e) {
            Log.e(TAG, "播放Opus数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查是否正在录音
     */
    public boolean isRecording() {
        return isRecording.get();
    }

    /**
     * 检查是否正在播放
     */
    public boolean isPlaying() {
        return isPlaying.get() && audioTrack != null && audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING;
    }

    /**
     * 停止播放
     */
    public void stopPlaying() {
        if (!isPlaying.get()) {
            return;
        }
        
        try {
            if (audioTrack != null) {
                audioTrack.pause();
                audioTrack.flush();
                isPlaying.set(false);
                Log.d(TAG, "停止音频播放");
            }
        } catch (Exception e) {
            Log.e(TAG, "停止播放时出错: " + e.getMessage(), e);
        }
    }
}