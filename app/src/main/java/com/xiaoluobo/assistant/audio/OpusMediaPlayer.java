package com.xiaoluobo.assistant.audio;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.MediaPlayer;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Opus音频播放器
 * 使用MediaPlayer播放opus解码后的音频，提供更好的音频回声处理
 */
public class OpusMediaPlayer {
    
    private static final String TAG = "OpusMediaPlayer";
    
    private Context context;
    private MediaPlayer mediaPlayer;
    private Handler mainHandler;
    
    // 音频数据队列
    private final BlockingQueue<byte[]> audioQueue = new LinkedBlockingQueue<>();
    
    // 播放状态
    private final AtomicBoolean isPlaying = new AtomicBoolean(false);
    private final AtomicBoolean isPreparing = new AtomicBoolean(false);
    private final AtomicBoolean shouldStop = new AtomicBoolean(false);
    
    // 临时音频文件
    private File tempAudioFile;
    private FileOutputStream tempFileStream;
    
    // 音频处理线程
    private Thread audioProcessingThread;
    
    // 播放监听器
    public interface PlaybackListener {
        void onPlaybackStarted();
        void onPlaybackCompleted();
        void onPlaybackError(String error);
    }
    
    private PlaybackListener playbackListener;
    
    /**
     * 构造函数
     */
    public OpusMediaPlayer(Context context) {
        this.context = context.getApplicationContext();
        this.mainHandler = new Handler(Looper.getMainLooper());
        initializeMediaPlayer();
        createTempAudioFile();
    }
    
    /**
     * 初始化MediaPlayer
     */
    private void initializeMediaPlayer() {
        mediaPlayer = new MediaPlayer();
        
        // 设置音频属性 - 专门用于语音播放
        AudioAttributes audioAttributes = new AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_ASSISTANT)  // 语音助手用途
            .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)  // 语音内容
            .build();
        mediaPlayer.setAudioAttributes(audioAttributes);
        
        // 设置播放完成监听器
        mediaPlayer.setOnCompletionListener(mp -> {
            Log.i(TAG, "AI语音播放完成");
            isPlaying.set(false);
            
            // 通知监听器
            if (playbackListener != null) {
                mainHandler.post(() -> playbackListener.onPlaybackCompleted());
            }
            
            // 检查是否还有待播放的音频
            if (!audioQueue.isEmpty() && !shouldStop.get()) {
                processNextAudioChunk();
            }
        });
        
        // 设置错误监听器
        mediaPlayer.setOnErrorListener((mp, what, extra) -> {
            String errorMsg = "MediaPlayer错误: what=" + what + ", extra=" + extra;
            Log.e(TAG, errorMsg);
            
            isPlaying.set(false);
            isPreparing.set(false);
            
            // 通知监听器
            if (playbackListener != null) {
                mainHandler.post(() -> playbackListener.onPlaybackError(errorMsg));
            }
            
            return false;
        });
        
        // 设置准备完成监听器
        mediaPlayer.setOnPreparedListener(mp -> {
            Log.i(TAG, "AI语音准备完成，开始播放");
            isPreparing.set(false);
            
            try {
                mp.start();
                isPlaying.set(true);
                
                // 通知监听器
                if (playbackListener != null) {
                    mainHandler.post(() -> playbackListener.onPlaybackStarted());
                }
            } catch (Exception e) {
                Log.e(TAG, "开始播放失败", e);
                isPlaying.set(false);
            }
        });
        
        Log.i(TAG, "MediaPlayer初始化完成");
    }
    
    /**
     * 创建临时音频文件
     */
    private void createTempAudioFile() {
        try {
            // 在应用缓存目录创建临时文件
            File cacheDir = context.getCacheDir();
            tempAudioFile = new File(cacheDir, "ai_speech_" + System.currentTimeMillis() + ".wav");
            
            Log.i(TAG, "创建临时音频文件: " + tempAudioFile.getAbsolutePath());
        } catch (Exception e) {
            Log.e(TAG, "创建临时音频文件失败", e);
        }
    }
    
    /**
     * 设置播放监听器
     */
    public void setPlaybackListener(PlaybackListener listener) {
        this.playbackListener = listener;
    }
    
    /**
     * 添加opus音频数据
     */
    public void addOpusAudioData(byte[] opusData) {
        if (opusData != null && opusData.length > 0) {
            try {
                audioQueue.offer(opusData);
                Log.d(TAG, "添加opus音频数据: " + opusData.length + " bytes");
                
                // 如果当前没有在播放，开始处理音频
                if (!isPlaying.get() && !isPreparing.get()) {
                    startAudioProcessing();
                }
            } catch (Exception e) {
                Log.e(TAG, "添加音频数据失败", e);
            }
        }
    }
    
    /**
     * 开始音频处理
     */
    private void startAudioProcessing() {
        if (audioProcessingThread != null && audioProcessingThread.isAlive()) {
            return;
        }
        
        shouldStop.set(false);
        audioProcessingThread = new Thread(this::processAudioData, "OpusAudioProcessor");
        audioProcessingThread.start();
        
        Log.i(TAG, "开始音频处理线程");
    }
    
    /**
     * 处理音频数据
     */
    private void processAudioData() {
        try {
            // 创建新的临时文件
            createTempAudioFile();
            tempFileStream = new FileOutputStream(tempAudioFile);
            
            // 写入WAV文件头（44字节）
            writeWavHeader(tempFileStream);
            
            int totalDataSize = 0;
            
            // 处理音频队列中的数据
            while (!shouldStop.get()) {
                try {
                    // 从队列中取出opus数据（阻塞等待）
                    byte[] opusData = audioQueue.poll(100, java.util.concurrent.TimeUnit.MILLISECONDS);
                    
                    if (opusData != null) {
                        // 解码opus数据为PCM
                        byte[] pcmData = decodeOpusData(opusData);
                        
                        if (pcmData != null && pcmData.length > 0) {
                            // 写入PCM数据到临时文件
                            tempFileStream.write(pcmData);
                            totalDataSize += pcmData.length;
                            
                            Log.d(TAG, "写入PCM数据: " + pcmData.length + " bytes");
                        }
                    } else {
                        // 队列为空，检查是否应该开始播放
                        if (totalDataSize > 0 && !isPlaying.get() && !isPreparing.get()) {
                            startPlayback(totalDataSize);
                            break;
                        }
                    }
                } catch (InterruptedException e) {
                    Log.i(TAG, "音频处理线程被中断");
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "处理音频数据异常", e);
                    break;
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "音频处理失败", e);
        } finally {
            // 关闭文件流
            if (tempFileStream != null) {
                try {
                    tempFileStream.close();
                } catch (IOException e) {
                    Log.e(TAG, "关闭临时文件流失败", e);
                }
            }
        }
    }
    
    /**
     * 开始播放
     */
    private void startPlayback(int dataSize) {
        try {
            // 更新WAV文件头中的数据大小
            updateWavHeader(tempAudioFile, dataSize);
            
            // 准备MediaPlayer
            isPreparing.set(true);
            mediaPlayer.reset();
            mediaPlayer.setDataSource(tempAudioFile.getAbsolutePath());
            mediaPlayer.prepareAsync();
            
            Log.i(TAG, "开始准备播放AI语音，数据大小: " + dataSize + " bytes");
            
        } catch (Exception e) {
            Log.e(TAG, "准备播放失败", e);
            isPreparing.set(false);
            
            if (playbackListener != null) {
                mainHandler.post(() -> playbackListener.onPlaybackError("准备播放失败: " + e.getMessage()));
            }
        }
    }
    
    /**
     * 处理下一个音频块
     */
    private void processNextAudioChunk() {
        if (!audioQueue.isEmpty()) {
            startAudioProcessing();
        }
    }
    
    /**
     * 解码opus数据为PCM
     * 这里需要使用opus解码库，暂时返回原始数据作为示例
     */
    private byte[] decodeOpusData(byte[] opusData) {
        // TODO: 实现opus解码
        // 这里应该使用opus解码库将opus数据解码为PCM数据
        // 暂时返回原始数据作为示例
        Log.d(TAG, "解码opus数据: " + opusData.length + " bytes");
        return opusData;
    }
    
    /**
     * 写入WAV文件头
     */
    private void writeWavHeader(FileOutputStream fos) throws IOException {
        // WAV文件头（44字节）
        // 这里写入标准的16位PCM WAV文件头
        byte[] header = new byte[44];
        
        // "RIFF"
        header[0] = 'R'; header[1] = 'I'; header[2] = 'F'; header[3] = 'F';
        // 文件大小（稍后更新）
        // "WAVE"
        header[8] = 'W'; header[9] = 'A'; header[10] = 'V'; header[11] = 'E';
        // "fmt "
        header[12] = 'f'; header[13] = 'm'; header[14] = 't'; header[15] = ' ';
        // fmt chunk size (16)
        header[16] = 16; header[17] = 0; header[18] = 0; header[19] = 0;
        // audio format (1 = PCM)
        header[20] = 1; header[21] = 0;
        // channels (1 = mono)
        header[22] = 1; header[23] = 0;
        // sample rate (16000 Hz)
        header[24] = (byte)(16000 & 0xff); header[25] = (byte)((16000 >> 8) & 0xff);
        header[26] = (byte)((16000 >> 16) & 0xff); header[27] = (byte)((16000 >> 24) & 0xff);
        // byte rate (sample rate * channels * bits per sample / 8)
        int byteRate = 16000 * 1 * 16 / 8;
        header[28] = (byte)(byteRate & 0xff); header[29] = (byte)((byteRate >> 8) & 0xff);
        header[30] = (byte)((byteRate >> 16) & 0xff); header[31] = (byte)((byteRate >> 24) & 0xff);
        // block align (channels * bits per sample / 8)
        header[32] = 2; header[33] = 0;
        // bits per sample (16)
        header[34] = 16; header[35] = 0;
        // "data"
        header[36] = 'd'; header[37] = 'a'; header[38] = 't'; header[39] = 'a';
        // data size（稍后更新）
        
        fos.write(header);
    }
    
    /**
     * 更新WAV文件头中的数据大小
     */
    private void updateWavHeader(File file, int dataSize) {
        // TODO: 实现WAV文件头更新
        // 更新文件大小和数据大小字段
        Log.d(TAG, "更新WAV文件头，数据大小: " + dataSize);
    }

    /**
     * 停止播放
     */
    public void stop() {
        Log.i(TAG, "停止AI语音播放");

        shouldStop.set(true);

        // 停止MediaPlayer
        if (mediaPlayer != null) {
            try {
                if (isPlaying.get()) {
                    mediaPlayer.stop();
                }
                isPlaying.set(false);
                isPreparing.set(false);
            } catch (Exception e) {
                Log.e(TAG, "停止MediaPlayer失败", e);
            }
        }

        // 清空音频队列
        audioQueue.clear();

        // 中断音频处理线程
        if (audioProcessingThread != null && audioProcessingThread.isAlive()) {
            audioProcessingThread.interrupt();
        }

        // 关闭文件流
        if (tempFileStream != null) {
            try {
                tempFileStream.close();
                tempFileStream = null;
            } catch (IOException e) {
                Log.e(TAG, "关闭文件流失败", e);
            }
        }
    }

    /**
     * 暂停播放
     */
    public void pause() {
        if (mediaPlayer != null && isPlaying.get()) {
            try {
                mediaPlayer.pause();
                isPlaying.set(false);
                Log.i(TAG, "暂停AI语音播放");
            } catch (Exception e) {
                Log.e(TAG, "暂停播放失败", e);
            }
        }
    }

    /**
     * 恢复播放
     */
    public void resume() {
        if (mediaPlayer != null && !isPlaying.get()) {
            try {
                mediaPlayer.start();
                isPlaying.set(true);
                Log.i(TAG, "恢复AI语音播放");
            } catch (Exception e) {
                Log.e(TAG, "恢复播放失败", e);
            }
        }
    }

    /**
     * 获取播放状态
     */
    public boolean isPlaying() {
        return isPlaying.get();
    }

    /**
     * 获取准备状态
     */
    public boolean isPreparing() {
        return isPreparing.get();
    }

    /**
     * 设置音量
     */
    public void setVolume(float volume) {
        if (mediaPlayer != null) {
            try {
                mediaPlayer.setVolume(volume, volume);
                Log.d(TAG, "设置AI语音音量: " + volume);
            } catch (Exception e) {
                Log.e(TAG, "设置音量失败", e);
            }
        }
    }

    /**
     * 清理资源
     */
    public void release() {
        Log.i(TAG, "释放OpusMediaPlayer资源");

        // 停止播放
        stop();

        // 释放MediaPlayer
        if (mediaPlayer != null) {
            try {
                mediaPlayer.release();
                mediaPlayer = null;
            } catch (Exception e) {
                Log.e(TAG, "释放MediaPlayer失败", e);
            }
        }

        // 删除临时文件
        if (tempAudioFile != null && tempAudioFile.exists()) {
            try {
                tempAudioFile.delete();
                Log.d(TAG, "删除临时音频文件");
            } catch (Exception e) {
                Log.e(TAG, "删除临时文件失败", e);
            }
        }

        // 清理监听器
        playbackListener = null;
    }

    /**
     * 获取当前播放位置
     */
    public int getCurrentPosition() {
        if (mediaPlayer != null && isPlaying.get()) {
            try {
                return mediaPlayer.getCurrentPosition();
            } catch (Exception e) {
                Log.e(TAG, "获取播放位置失败", e);
            }
        }
        return 0;
    }

    /**
     * 获取音频总时长
     */
    public int getDuration() {
        if (mediaPlayer != null) {
            try {
                return mediaPlayer.getDuration();
            } catch (Exception e) {
                Log.e(TAG, "获取音频时长失败", e);
            }
        }
        return 0;
    }
}
