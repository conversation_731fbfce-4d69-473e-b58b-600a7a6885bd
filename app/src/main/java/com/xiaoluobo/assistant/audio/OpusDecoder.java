package com.xiaoluobo.assistant.audio;

import android.util.Log;

/**
 * Opus音频解码器
 * 负责将opus编码的音频数据解码为PCM数据
 */
public class OpusDecoder {
    
    private static final String TAG = "OpusDecoder";
    
    // 音频参数
    private static final int SAMPLE_RATE = 16000;  // 16kHz采样率
    private static final int CHANNELS = 1;         // 单声道
    private static final int FRAME_SIZE = 320;     // 每帧样本数 (20ms * 16kHz)
    
    // 解码器状态
    private boolean isInitialized = false;
    private long decoderHandle = 0;  // native解码器句柄
    
    /**
     * 初始化解码器
     */
    public boolean initialize() {
        try {
            // 初始化native opus解码器
            decoderHandle = nativeInit(SAMPLE_RATE, CHANNELS);
            
            if (decoderHandle != 0) {
                isInitialized = true;
                Log.i(TAG, "Opus解码器初始化成功");
                return true;
            } else {
                Log.e(TAG, "Opus解码器初始化失败");
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "初始化Opus解码器异常", e);
            return false;
        }
    }
    
    /**
     * 解码opus数据为PCM
     * 
     * @param opusData opus编码的音频数据
     * @return PCM音频数据，如果解码失败返回null
     */
    public byte[] decode(byte[] opusData) {
        if (!isInitialized || decoderHandle == 0) {
            Log.e(TAG, "解码器未初始化");
            return null;
        }
        
        if (opusData == null || opusData.length == 0) {
            Log.w(TAG, "输入的opus数据为空");
            return null;
        }
        
        try {
            // 调用native方法解码
            byte[] pcmData = nativeDecode(decoderHandle, opusData, opusData.length);
            
            if (pcmData != null && pcmData.length > 0) {
                Log.d(TAG, "解码成功: " + opusData.length + " bytes opus -> " + pcmData.length + " bytes PCM");
                return pcmData;
            } else {
                Log.w(TAG, "解码失败或返回空数据");
                return null;
            }
        } catch (Exception e) {
            Log.e(TAG, "解码opus数据异常", e);
            return null;
        }
    }
    
    /**
     * 批量解码多个opus数据包
     * 
     * @param opusPackets opus数据包数组
     * @return 合并后的PCM数据
     */
    public byte[] decodeMultiple(byte[][] opusPackets) {
        if (opusPackets == null || opusPackets.length == 0) {
            return null;
        }
        
        // 计算总的PCM数据大小
        int totalPcmSize = 0;
        byte[][] pcmPackets = new byte[opusPackets.length][];
        
        // 逐个解码
        for (int i = 0; i < opusPackets.length; i++) {
            pcmPackets[i] = decode(opusPackets[i]);
            if (pcmPackets[i] != null) {
                totalPcmSize += pcmPackets[i].length;
            }
        }
        
        if (totalPcmSize == 0) {
            return null;
        }
        
        // 合并PCM数据
        byte[] combinedPcm = new byte[totalPcmSize];
        int offset = 0;
        
        for (byte[] pcmPacket : pcmPackets) {
            if (pcmPacket != null) {
                System.arraycopy(pcmPacket, 0, combinedPcm, offset, pcmPacket.length);
                offset += pcmPacket.length;
            }
        }
        
        Log.d(TAG, "批量解码完成: " + opusPackets.length + " 个包 -> " + totalPcmSize + " bytes PCM");
        return combinedPcm;
    }
    
    /**
     * 释放解码器资源
     */
    public void release() {
        if (isInitialized && decoderHandle != 0) {
            try {
                nativeRelease(decoderHandle);
                decoderHandle = 0;
                isInitialized = false;
                Log.i(TAG, "Opus解码器资源已释放");
            } catch (Exception e) {
                Log.e(TAG, "释放Opus解码器资源异常", e);
            }
        }
    }
    
    /**
     * 检查解码器是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized && decoderHandle != 0;
    }
    
    /**
     * 获取解码器信息
     */
    public String getDecoderInfo() {
        return String.format("OpusDecoder[采样率:%dHz, 声道:%d, 帧大小:%d, 状态:%s]", 
            SAMPLE_RATE, CHANNELS, FRAME_SIZE, isInitialized ? "已初始化" : "未初始化");
    }
    
    // ==================== Native方法声明 ====================
    
    /**
     * 初始化native opus解码器
     * 
     * @param sampleRate 采样率
     * @param channels 声道数
     * @return 解码器句柄，失败返回0
     */
    private native long nativeInit(int sampleRate, int channels);
    
    /**
     * 解码opus数据
     * 
     * @param handle 解码器句柄
     * @param opusData opus编码数据
     * @param dataSize 数据大小
     * @return PCM数据，失败返回null
     */
    private native byte[] nativeDecode(long handle, byte[] opusData, int dataSize);
    
    /**
     * 释放native解码器
     * 
     * @param handle 解码器句柄
     */
    private native void nativeRelease(long handle);
    
    // ==================== 静态初始化 ====================
    
    static {
        try {
            // 加载native库
            System.loadLibrary("opus_decoder");
            Log.i(TAG, "Opus native库加载成功");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Opus native库加载失败", e);
            // 如果native库加载失败，可以考虑使用Java实现的备用方案
        }
    }
    
    // ==================== 备用实现（如果native库不可用） ====================
    
    /**
     * 简单的备用解码实现（仅用于测试）
     * 实际项目中应该使用真正的opus解码库
     */
    private byte[] fallbackDecode(byte[] opusData) {
        Log.w(TAG, "使用备用解码实现（仅用于测试）");
        
        // 这里只是简单地返回原始数据作为PCM
        // 实际应用中需要真正的opus解码
        return opusData;
    }
    
    /**
     * 检查native库是否可用
     */
    public static boolean isNativeLibraryAvailable() {
        try {
            // 尝试调用一个native方法来检查库是否可用
            OpusDecoder testDecoder = new OpusDecoder();
            return testDecoder.nativeInit(16000, 1) != 0;
        } catch (Exception e) {
            return false;
        }
    }
}
