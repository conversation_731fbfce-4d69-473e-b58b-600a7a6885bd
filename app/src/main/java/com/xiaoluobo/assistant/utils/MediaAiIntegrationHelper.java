package com.xiaoluobo.assistant.utils;

import android.util.Log;
import com.xiaoluobo.assistant.iot.ThingManager;
import com.xiaoluobo.assistant.iot.things.LocalMediaPlayer;

/**
 * 媒体播放器与AI语音交互的集成辅助类
 * 负责协调AI语音服务与本地媒体播放器之间的交互
 */
public class MediaAiIntegrationHelper implements LocalMediaPlayer.AiStateListener {
    
    private static final String TAG = "MediaAiIntegrationHelper";
    private static MediaAiIntegrationHelper instance;
    
    private LocalMediaPlayer mediaPlayer;
    private AiServiceCallback aiServiceCallback;
    
    /**
     * AI服务回调接口
     */
    public interface AiServiceCallback {
        void exitAiChat();
        void notifyMediaPlaybackStarted();
        void notifyAiStateChanged(String state);
    }
    
    private MediaAiIntegrationHelper() {
        // 私有构造函数，单例模式
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized MediaAiIntegrationHelper getInstance() {
        if (instance == null) {
            instance = new MediaAiIntegrationHelper();
        }
        return instance;
    }
    
    /**
     * 初始化集成助手
     */
    public void initialize(ThingManager thingManager, AiServiceCallback callback) {
        this.aiServiceCallback = callback;
        
        // 获取LocalMediaPlayer实例
        mediaPlayer = (LocalMediaPlayer) thingManager.getThing("LocalMediaPlayer");
        if (mediaPlayer != null) {
            mediaPlayer.setAiStateListener(this);
            Log.i(TAG, "媒体播放器AI集成初始化完成");
        } else {
            Log.e(TAG, "未找到LocalMediaPlayer实例");
        }
    }
    
    /**
     * 通知AI聊天开始
     */
    public void notifyAiChatStarted() {
        if (mediaPlayer != null) {
            mediaPlayer.handleAiChatStart();
        }
    }
    
    /**
     * 通知AI聊天结束
     */
    public void notifyAiChatEnded() {
        if (mediaPlayer != null) {
            mediaPlayer.handleAiChatEnd();
        }
    }
    
    /**
     * 通知AI开始说话
     */
    public void notifyAiSpeakingStarted() {
        if (mediaPlayer != null) {
            mediaPlayer.handleAiSpeakStart();
        }
    }
    
    /**
     * 通知AI结束说话
     */
    public void notifyAiSpeakingEnded() {
        if (mediaPlayer != null) {
            mediaPlayer.handleAiSpeakEnd();
        }
    }
    
    /**
     * 设置AI说话时的音量
     */
    public void setDuckedVolume(float volume) {
        if (mediaPlayer != null) {
            mediaPlayer.setDuckedVolume(String.valueOf(volume));
        }
    }
    
    /**
     * 获取当前AI状态
     */
    public boolean isAiChatActive() {
        return mediaPlayer != null && mediaPlayer.isAiChatActive();
    }
    
    /**
     * 获取AI是否正在说话
     */
    public boolean isAiSpeaking() {
        return mediaPlayer != null && mediaPlayer.isAiSpeaking();
    }
    
    // ==================== AiStateListener 实现 ====================
    
    @Override
    public void onAiChatStarted() {
        Log.i(TAG, "AI聊天开始回调");
        if (aiServiceCallback != null) {
            aiServiceCallback.notifyAiStateChanged("chat_started");
        }
    }
    
    @Override
    public void onAiChatEnded() {
        Log.i(TAG, "AI聊天结束回调");
        if (aiServiceCallback != null) {
            aiServiceCallback.notifyAiStateChanged("chat_ended");
        }
    }
    
    @Override
    public void onAiSpeakingStarted() {
        Log.i(TAG, "AI开始说话回调");
        if (aiServiceCallback != null) {
            aiServiceCallback.notifyAiStateChanged("speaking_started");
        }
    }
    
    @Override
    public void onAiSpeakingEnded() {
        Log.i(TAG, "AI结束说话回调");
        if (aiServiceCallback != null) {
            aiServiceCallback.notifyAiStateChanged("speaking_ended");
        }
    }
    
    @Override
    public void onMediaPlaybackRequested() {
        Log.i(TAG, "媒体播放请求回调，准备退出AI聊天");
        if (aiServiceCallback != null) {
            // 通知AI服务退出聊天模式
            aiServiceCallback.exitAiChat();
            aiServiceCallback.notifyMediaPlaybackStarted();
        }
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 快速集成到语音服务
     * 在语音服务的关键生命周期方法中调用
     */
    public static class VoiceServiceIntegration {
        
        private static MediaAiIntegrationHelper helper = MediaAiIntegrationHelper.getInstance();
        
        /**
         * 在语音识别开始时调用
         */
        public static void onVoiceRecognitionStarted() {
            helper.notifyAiChatStarted();
            Log.d(TAG, "语音识别开始，已通知媒体播放器");
        }
        
        /**
         * 在语音识别结束时调用
         */
        public static void onVoiceRecognitionEnded() {
            // 注意：这里不直接结束AI聊天，因为可能还要播放AI回复
            Log.d(TAG, "语音识别结束");
        }
        
        /**
         * 在AI开始播放回复时调用
         */
        public static void onAiResponsePlaybackStarted() {
            helper.notifyAiSpeakingStarted();
            Log.d(TAG, "AI开始播放回复，已降低媒体音量");
        }
        
        /**
         * 在AI回复播放结束时调用
         */
        public static void onAiResponsePlaybackEnded() {
            helper.notifyAiSpeakingEnded();
            Log.d(TAG, "AI回复播放结束，已恢复媒体音量");
        }
        
        /**
         * 在完全退出AI聊天时调用
         */
        public static void onExitAiChat() {
            helper.notifyAiChatEnded();
            Log.d(TAG, "退出AI聊天，已恢复媒体播放状态");
        }
        
        /**
         * 设置音量降低程度
         */
        public static void setVolumeReduction(float duckedVolume) {
            helper.setDuckedVolume(duckedVolume);
            Log.d(TAG, "设置AI说话时的音量: " + duckedVolume);
        }
    }
    
    // ==================== 使用示例 ====================
    
    /**
     * 使用示例：在VoiceCommunicationService中的集成
     * 
     * // 1. 在服务初始化时
     * MediaAiIntegrationHelper.getInstance().initialize(thingManager, new AiServiceCallback() {
     *     @Override
     *     public void exitAiChat() {
     *         // 退出AI聊天模式的逻辑
     *         stopListening();
     *         hideAiInterface();
     *     }
     *     
     *     @Override
     *     public void notifyMediaPlaybackStarted() {
     *         // 媒体开始播放的通知
     *         showMediaPlayingIndicator();
     *     }
     *     
     *     @Override
     *     public void notifyAiStateChanged(String state) {
     *         // AI状态变化的通知
     *         updateAiStatusIndicator(state);
     *     }
     * });
     * 
     * // 2. 在语音交互的关键节点调用
     * MediaAiIntegrationHelper.VoiceServiceIntegration.onVoiceRecognitionStarted();
     * MediaAiIntegrationHelper.VoiceServiceIntegration.onAiResponsePlaybackStarted();
     * MediaAiIntegrationHelper.VoiceServiceIntegration.onAiResponsePlaybackEnded();
     * MediaAiIntegrationHelper.VoiceServiceIntegration.onExitAiChat();
     */
}
