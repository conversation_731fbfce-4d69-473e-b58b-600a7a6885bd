package com.xiaoluobo.assistant.utils;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import com.xiaoluobo.assistant.iot.ThingManager;
import com.xiaoluobo.assistant.iot.things.LocalMediaPlayer;
import com.xiaoluobo.assistant.service.AiChatService;

/**
 * 媒体播放器与AI语音交互的集成辅助类
 * 负责协调新的AiChatService与LocalMediaPlayer之间的交互
 * 
 * 架构说明：
 * - AiChatService: 新的优化AI聊天服务，复用protocols协议
 * - LocalMediaPlayer: IoT框架中的本地媒体播放器
 * - 本类: 协调两者之间的交互，实现智能音频管理
 */
public class MediaAiIntegrationHelper implements LocalMediaPlayer.AiStateListener, AiChatService.AiChatListener {
    
    private static final String TAG = "MediaAiIntegrationHelper";
    private static MediaAiIntegrationHelper instance;
    
    private Context context;
    private LocalMediaPlayer mediaPlayer;
    private AiChatService aiChatService;
    
    private MediaAiIntegrationHelper() {
        // 私有构造函数，单例模式
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized MediaAiIntegrationHelper getInstance() {
        if (instance == null) {
            instance = new MediaAiIntegrationHelper();
        }
        return instance;
    }
    
    /**
     * 初始化集成助手
     */
    public void initialize(Context context) {
        this.context = context.getApplicationContext();
        
        // 获取LocalMediaPlayer实例
        ThingManager thingManager = ThingManager.getInstance();
        mediaPlayer = (LocalMediaPlayer) thingManager.getThing("LocalMediaPlayer");
        
        if (mediaPlayer != null) {
            mediaPlayer.setAiStateListener(this);
            Log.i(TAG, "LocalMediaPlayer集成初始化完成");
        } else {
            Log.e(TAG, "未找到LocalMediaPlayer实例");
        }
        
        // 启动AiChatService
        startAiChatService();
        
        Log.i(TAG, "媒体AI集成助手初始化完成");
    }
    
    /**
     * 启动AI聊天服务
     */
    private void startAiChatService() {
        if (context != null) {
            Intent serviceIntent = new Intent(context, AiChatService.class);
            context.startForegroundService(serviceIntent);
            
            // 等待服务启动后获取实例
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                aiChatService = AiChatService.getInstance();
                if (aiChatService != null) {
                    aiChatService.setAiChatListener(this);
                    Log.i(TAG, "AiChatService集成完成");
                } else {
                    Log.w(TAG, "AiChatService实例获取失败，稍后重试");
                    // 重试获取服务实例
                    retryGetAiChatService();
                }
            }, 1000);
        }
    }
    
    /**
     * 重试获取AI聊天服务实例
     */
    private void retryGetAiChatService() {
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            aiChatService = AiChatService.getInstance();
            if (aiChatService != null) {
                aiChatService.setAiChatListener(this);
                Log.i(TAG, "AiChatService集成完成（重试成功）");
            } else {
                Log.e(TAG, "AiChatService实例获取失败");
            }
        }, 2000);
    }
    
    // ==================== 公共API ====================
    
    /**
     * 开始AI聊天
     */
    public void startAiChat() {
        if (aiChatService != null) {
            aiChatService.startChat();
        } else {
            Log.w(TAG, "AiChatService未初始化，无法开始聊天");
        }
    }
    
    /**
     * 结束AI聊天
     */
    public void endAiChat() {
        if (aiChatService != null) {
            aiChatService.endChat();
        } else {
            Log.w(TAG, "AiChatService未初始化，无法结束聊天");
        }
    }
    
    /**
     * 开始监听用户语音
     */
    public void startListening() {
        if (aiChatService != null) {
            aiChatService.startListening();
        } else {
            Log.w(TAG, "AiChatService未初始化，无法开始监听");
        }
    }
    
    /**
     * 停止监听用户语音
     */
    public void stopListening() {
        if (aiChatService != null) {
            aiChatService.stopListening();
        } else {
            Log.w(TAG, "AiChatService未初始化，无法停止监听");
        }
    }
    
    /**
     * 获取AI聊天状态
     */
    public boolean isAiChatActive() {
        return aiChatService != null && aiChatService.isChatActive();
    }
    
    /**
     * 获取AI是否正在说话
     */
    public boolean isAiSpeaking() {
        return aiChatService != null && aiChatService.isAiSpeaking();
    }
    
    /**
     * 获取是否正在监听
     */
    public boolean isListening() {
        return aiChatService != null && aiChatService.isListening();
    }
    
    // ==================== LocalMediaPlayer.AiStateListener 实现 ====================
    
    @Override
    public void onAiChatStarted() {
        Log.i(TAG, "媒体播放器通知：AI聊天开始");
    }
    
    @Override
    public void onAiChatEnded() {
        Log.i(TAG, "媒体播放器通知：AI聊天结束");
    }
    
    @Override
    public void onAiSpeakingStarted() {
        Log.i(TAG, "媒体播放器通知：AI开始说话");
    }
    
    @Override
    public void onAiSpeakingEnded() {
        Log.i(TAG, "媒体播放器通知：AI结束说话");
    }
    
    @Override
    public void onMediaPlaybackRequested() {
        Log.i(TAG, "媒体播放器请求：开始播放媒体，准备退出AI聊天");
        // 延迟退出AI聊天，确保媒体开始播放
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            endAiChat();
        }, 1500);
    }
    
    // ==================== AiChatService.AiChatListener 实现 ====================
    
    @Override
    public void onChatStarted() {
        Log.i(TAG, "AI聊天服务通知：聊天开始");
    }
    
    @Override
    public void onChatEnded() {
        Log.i(TAG, "AI聊天服务通知：聊天结束");
    }
    
    @Override
    public void onAiSpeakingStarted() {
        Log.i(TAG, "AI聊天服务通知：AI开始说话");
    }
    
    @Override
    public void onAiSpeakingEnded() {
        Log.i(TAG, "AI聊天服务通知：AI结束说话");
    }
    
    @Override
    public void onListeningStarted() {
        Log.i(TAG, "AI聊天服务通知：开始监听");
    }
    
    @Override
    public void onListeningEnded() {
        Log.i(TAG, "AI聊天服务通知：结束监听");
    }
    
    @Override
    public void onError(String error) {
        Log.e(TAG, "AI聊天服务错误：" + error);
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 快速集成方法，用于在Application中初始化
     */
    public static void quickSetup(Context context) {
        MediaAiIntegrationHelper.getInstance().initialize(context);
    }
    
    /**
     * 检查服务是否就绪
     */
    public boolean isReady() {
        return mediaPlayer != null && aiChatService != null;
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (!isReady()) {
            return "服务未就绪";
        }
        
        if (isAiChatActive()) {
            if (isAiSpeaking()) {
                return "AI正在说话";
            } else if (isListening()) {
                return "正在监听用户";
            } else {
                return "AI聊天活跃";
            }
        } else {
            return "AI聊天空闲";
        }
    }
}
