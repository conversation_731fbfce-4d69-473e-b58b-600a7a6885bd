# 小萝卜故事机 - LocalMediaPlayer 使用说明

## 概述

小萝卜故事机是一个专为儿童设计的本地媒体播放器IoT设备，通过AI语音交互来播放本地存储的儿童故事和儿歌。

## 核心特性

### 1. 智能数据库管理
- **SQLite数据库**: 使用本地数据库管理媒体文件元数据
- **智能分类**: 自动根据文件夹结构分类内容
- **标签系统**: 为每个媒体文件生成智能标签，便于AI检索
- **年龄分组**: 根据内容类型自动分配适合年龄组

### 2. 儿童友好的交互方式
- **方法命名**: 使用"小萝卜讲故事"、"小萝卜唱儿歌"等儿童友好的方法名
- **智能搜索**: 支持自然语言搜索，如"我想听小红帽的故事"
- **主题播放**: 支持按主题播放，如"播放安全教育内容"

### 3. 优化的数据传输
- **精简属性**: 只向服务端发送必要的统计信息，不发送完整文件列表
- **关键词提取**: 提供故事和儿歌关键词，便于AI理解和匹配
- **分类摘要**: 发送分类名称和数量，而非详细文件列表

## 媒体文件部署

### 推荐目录结构
```
/storage/sdcard0/Media/
├── 儿童故事《夏风入梦精选1047篇》/
│   ├── 0001集 不肯长大的小泰莱莎.mp3
│   ├── 0002集 胡桃夹子.mp3
│   └── ...
├── 宝宝巴士儿歌大全586首/
│   ├── 1.【经典儿歌】懂礼貌.mp3
│   ├── 2.【经典儿歌】我爱洗澡.mp3
│   └── ...
└── 儿童睡前故事《讲给孩子的三十六计》/
    ├── 01 三十六计 第一计 瞒天过海.mp3
    └── ...
```

### 备用路径
如果主路径不存在，系统会自动尝试以下路径：
1. `/storage/emulated/0/Media`
2. `[SD卡根目录]/Media`
3. `[应用外部文件目录]/Media`

## 设备属性（发送给服务端的数据）

### 播放状态
- `isPlaying`: 小萝卜是否在播放
- `isPaused`: 小萝卜是否暂停了
- `isBuffering`: 小萝卜是否在加载

### 当前播放信息
- `currentStory`: 正在播放的故事
- `currentCategory`: 正在播放的分类
- `playMode`: 播放模式

### 媒体库统计（精简版）
- `libraryStats`: 故事库统计 (如: "故事:1047首,儿歌:586首,教育:36首")
- `categorySummary`: 分类摘要 (如: "儿童故事(1047),宝宝巴士儿歌(586)")

### AI检索关键词
- `storyKeywords`: 故事关键词列表
- `songKeywords`: 儿歌关键词列表

## 儿童友好的方法列表

### 播放控制
- `startPlaying`: 小萝卜开始播放
- `pauseStory`: 小萝卜暂停一下
- `stopPlaying`: 小萝卜停止播放
- `continueStory`: 小萝卜继续播放
- `nextStory`: 小萝卜播放下一个
- `previousStory`: 小萝卜播放上一个
- `replayStory`: 小萝卜重新播放这个

### 内容选择
- `playBedtimeStories`: 小萝卜讲睡前故事
- `playSongs`: 小萝卜唱儿歌
- `playEducationStories`: 小萝卜讲教育故事
- `tellStory(storyName)`: 小萝卜讲指定故事
- `singSong(songName)`: 小萝卜唱指定儿歌

### 随机播放
- `tellRandomStory`: 小萝卜随机讲故事
- `singRandomSong`: 小萝卜随机唱儿歌
- `playClassicTales`: 小萝卜讲经典童话
- `playSafetyEducation`: 小萝卜讲安全知识

### 智能搜索
- `findAndPlay(content)`: 小萝卜找到并播放
- `playForAge(age)`: 小萝卜播放适合年龄的内容
- `playByTheme(theme)`: 小萝卜播放主题内容
- `getRecommendations`: 小萝卜推荐内容

### 系统管理
- `refreshStoryLibrary`: 小萝卜更新故事库

## AI语音交互示例

### 用户说话示例及对应方法调用

**用户**: "小萝卜，我想听小红帽的故事"
**调用**: `findAndPlay("小红帽")`

**用户**: "小萝卜，给我唱首儿歌吧"
**调用**: `singRandomSong()`

**用户**: "小萝卜，我想听适合5岁孩子的故事"
**调用**: `playForAge("5岁")`

**用户**: "小萝卜，播放一些安全教育的内容"
**调用**: `playByTheme("安全")`

**用户**: "小萝卜，暂停一下"
**调用**: `pauseStory()`

**用户**: "小萝卜，继续播放"
**调用**: `continueStory()`

## 智能关键词映射

系统内置了智能关键词映射功能，可以将用户的自然语言转换为精确的搜索关键词：

### 经典童话映射
- "小红帽" → "小红帽"
- "白雪公主" → "白雪公主"
- "三只小猪" → "三只小猪"
- "龟兔赛跑" → "龟兔赛跑"

### 教育主题映射
- "安全教育" → "安全"
- "好习惯" → "习惯"
- "懂礼貌" → "礼貌"

### 年龄组映射
- 2-6岁 → 宝宝巴士儿歌
- 6-12岁 → 三十六计教育故事
- 3-8岁 → 经典儿童故事

## 数据库结构

### 媒体表 (media)
- `display_name`: 显示名称
- `category`: 分类
- `file_path`: 文件路径
- `tags`: 标签（用于搜索）
- `description`: 描述
- `age_group`: 适合年龄组

### 分类表 (categories)
- `category_name`: 分类名称
- `category_type`: 分类类型 (story/song/education)
- `category_desc`: 分类描述
- `media_count`: 媒体数量

## 性能优化

### 数据传输优化
1. **属性精简**: 只发送统计信息，不发送完整文件列表
2. **关键词提取**: 预定义关键词列表，减少搜索时间
3. **分类摘要**: 发送分类概要而非详细内容

### 搜索优化
1. **数据库索引**: 为搜索字段建立索引
2. **智能映射**: 预定义常用搜索词映射
3. **缓存机制**: 缓存常用搜索结果

## 故障排除

### 1. 媒体文件无法播放
- 检查文件路径: `/storage/sdcard0/Media/`
- 确认文件格式: 支持MP3, WAV, M4A, AAC, OGG, FLAC
- 检查文件权限

### 2. 搜索无结果
- 尝试使用关键词而非完整文件名
- 检查数据库是否正确建立
- 调用 `refreshStoryLibrary` 重新扫描

### 3. AI无法理解指令
- 使用预定义的关键词
- 检查 `storyKeywords` 和 `songKeywords` 属性
- 确认方法名称是否正确

## 扩展功能

未来可以添加的功能：
- 播放历史记录
- 收藏夹管理
- 家长控制
- 播放时长统计
- 内容推荐算法
- 多语言支持

## 总结

小萝卜故事机通过智能数据库管理、儿童友好的交互方式和优化的数据传输，为儿童提供了一个简单易用的本地媒体播放体验。系统设计充分考虑了AI语音交互的需求，通过精简的属性和智能的关键词映射，确保AI能够准确理解和响应儿童的需求。
