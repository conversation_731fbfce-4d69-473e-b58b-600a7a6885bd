# AI延迟播放功能 - LocalMediaPlayer

**上下文版本号: v1.0**

## 🎯 功能目标

解决AI下发播放故事指令时，AI还在继续说话的问题。实现智能延迟播放：
- **检测AI状态** - 判断AI是否正在说话
- **延迟播放** - 等待AI说完话再开始播放故事
- **智能退出** - 故事开始播放后自动退出AI聊天

## 🔄 工作流程

### 完整的交互流程
```
1. 用户: "小萝卜，播放小红帽的故事"
   状态: IDLE → LISTENING

2. AI: "好的，我来为你播放小红帽的故事"
   状态: LISTENING → SPEAKING
   系统: 检测到播放指令，但AI正在说话

3. 延迟播放逻辑:
   - 检测AI正在说话 (DeviceState.SPEAKING)
   - 延迟3秒等待AI说完话
   - 安排延迟播放任务

4. AI说完话后:
   - 延迟时间到，开始播放故事
   - 故事播放开始
   - 延迟2秒后退出AI聊天

5. 最终状态:
   状态: SPEAKING → IDLE
   播放: 故事正常播放
```

## 🧠 智能检测逻辑

### 1. **AI状态检测**
```java
private boolean shouldDelayPlaybackForAi() {
    // 检查设备状态是否为AI正在说话
    if (lastDeviceState == DeviceStateManager.DeviceState.SPEAKING) {
        Log.d(TAG, "检测到AI正在说话，需要延迟播放");
        return true;
    }
    return false;
}
```

### 2. **延迟播放调度**
```java
private void scheduleDelayedPlayback() {
    // 标记正在等待AI完成
    isWaitingForAiToFinish = true;
    
    // 创建延迟播放任务
    pendingPlayAction = new Runnable() {
        @Override
        public void run() {
            Log.i(TAG, "AI延迟时间到，开始播放故事");
            executePlayCurrentItem();      // 执行播放
            scheduleAiChatExit();         // 安排退出AI聊天
        }
    };
    
    // 延迟3秒执行
    handler.postDelayed(pendingPlayAction, AI_FINISH_DELAY);
}
```

### 3. **AI聊天退出**
```java
private void scheduleAiChatExit() {
    // 延迟2秒后退出AI聊天，确保故事播放已经开始
    handler.postDelayed(() -> {
        Log.i(TAG, "故事播放已开始，准备退出AI聊天");
        requestAiChatExit();
    }, 2000);
}
```

## ⏱️ 时间控制

### 延迟时间设置
- **AI完成延迟**: 3秒 (`AI_FINISH_DELAY = 3000ms`)
- **聊天退出延迟**: 2秒 (故事开始播放后)
- **总延迟时间**: 约5秒 (从指令下发到完全退出AI聊天)

### 时间线示例
```
T+0s:  用户说"播放小红帽"
T+1s:  AI开始回复"好的，我来为你播放..."
T+2s:  系统检测到播放指令，安排延迟播放
T+5s:  AI说完话，延迟时间到，开始播放故事
T+7s:  故事播放稳定，退出AI聊天
```

## 🎛️ 控制接口

### 新增IoT方法
- `cancelDelayedPlayback()` - 取消延迟播放
- `playImmediately()` - 立即播放（跳过延迟）
- `setAiFinishDelay(seconds)` - 设置AI完成延迟时间
- `getDelayedPlaybackInfo()` - 获取延迟播放信息

### 使用示例
```java
// 场景1: 取消延迟，立即播放
localMediaPlayer.playImmediately();

// 场景2: 取消当前的延迟播放任务
localMediaPlayer.cancelDelayedPlayback();

// 场景3: 查看延迟播放状态
localMediaPlayer.getDelayedPlaybackInfo();
```

## 📊 状态监控

### 新增属性
- `isWaitingForAi` - 是否在等待AI说完 (boolean)
- `aiFinishDelay` - AI完成延迟时间(秒) (number)
- `delayedPlaybackStatus` - 延迟播放状态 (string)

### 状态示例
```
isWaitingForAi: true
aiFinishDelay: 3
delayedPlaybackStatus: "等待AI说完话，延迟3秒播放"
```

## 🔄 退出AI聊天机制

### 多种通知方式
```java
private void requestAiChatExit() {
    // 方式1: 通过DeviceStateManager设置状态
    if (deviceStateManager != null) {
        Log.i(TAG, "通过DeviceStateManager请求退出AI聊天");
    }
    
    // 方式2: 通过广播通知
    Intent exitIntent = new Intent("com.xiaoluobo.assistant.EXIT_AI_CHAT");
    exitIntent.putExtra("reason", "media_playback_started");
    AssistantApplication.getContext().sendBroadcast(exitIntent);
    
    // 方式3: 可以添加其他通知方式
    // 比如通过AiChatService的接口、MQTT消息等
}
```

### 广播接收处理
在AiChatService或其他AI服务中需要监听广播：
```java
// 在AiChatService中注册广播接收器
IntentFilter filter = new IntentFilter("com.xiaoluobo.assistant.EXIT_AI_CHAT");
registerReceiver(aiChatExitReceiver, filter);

private BroadcastReceiver aiChatExitReceiver = new BroadcastReceiver() {
    @Override
    public void onReceive(Context context, Intent intent) {
        String reason = intent.getStringExtra("reason");
        Log.i(TAG, "收到退出AI聊天请求，原因: " + reason);
        
        if ("media_playback_started".equals(reason)) {
            // 媒体播放开始，退出AI聊天
            endChat();
        }
    }
};
```

## 🎯 使用场景

### 场景1: 正常延迟播放
```
用户: "播放小红帽的故事"
AI: "好的，我来为你播放小红帽的故事" (3秒)
系统: 检测到AI在说话，延迟播放
结果: 等待3秒后开始播放故事
```

### 场景2: 立即播放
```
用户: "播放小红帽的故事"
系统: 调用 playImmediately()
结果: 跳过延迟，立即开始播放
```

### 场景3: 取消延迟
```
用户: "播放小红帽的故事"
系统: 安排延迟播放
用户: "算了，不听了"
系统: 调用 cancelDelayedPlayback()
结果: 取消延迟播放任务
```

## 🔧 配置选项

### 延迟时间调整
```java
// 当前版本使用固定延迟时间
private static final int AI_FINISH_DELAY = 3000; // 3秒

// 未来可以改为动态配置
private int aiFinishDelay = 3000; // 可动态调整
```

### 检测条件扩展
```java
private boolean shouldDelayPlaybackForAi() {
    // 当前检测条件
    if (lastDeviceState == DeviceStateManager.DeviceState.SPEAKING) {
        return true;
    }
    
    // 可以添加更多检测条件：
    // - 检查AiChatService是否正在播放
    // - 检查音频输出状态
    // - 检查特定的AI状态标志
    
    return false;
}
```

## 📊 性能影响

### 内存使用
- **延迟任务**: 每个延迟播放任务约几KB内存
- **状态变量**: 几个boolean和Runnable变量，内存开销微不足道

### CPU使用
- **状态检测**: 简单的boolean判断，CPU开销极小
- **延迟调度**: 使用Handler.postDelayed，系统级优化

### 用户体验
- **正面影响**: 避免AI说话被故事打断，体验更自然
- **延迟感知**: 3秒延迟用户基本感觉不到（AI本来就在说话）

## 🔍 调试信息

### 关键日志
```
LocalMediaPlayer: AI正在说话，延迟播放: 小红帽的故事
LocalMediaPlayer: 已安排延迟播放，等待时间: 3000ms
LocalMediaPlayer: AI延迟时间到，开始播放故事
LocalMediaPlayer: 故事播放已开始，准备退出AI聊天
LocalMediaPlayer: 已发送退出AI聊天广播
```

### 状态检查
```java
// 检查延迟播放状态
String status = localMediaPlayer.getDelayedPlaybackStatus();
Log.i(TAG, "延迟播放状态: " + status);

// 检查是否在等待AI
boolean waiting = localMediaPlayer.isWaitingForAi();
Log.i(TAG, "是否在等待AI: " + waiting);
```

## ⚠️ 注意事项

1. **广播接收**: 确保AI服务正确注册和处理退出广播
2. **状态同步**: DeviceState的状态变化要及时准确
3. **延迟时间**: 3秒延迟适合大部分场景，特殊情况可调整
4. **资源清理**: 取消延迟播放时要正确清理Handler任务
5. **异常处理**: 延迟播放过程中的异常要妥善处理

## 🎉 总结

通过AI延迟播放功能：

- 🎭 **自然交互** - AI说完话再播放故事，避免语音冲突
- ⏱️ **智能延迟** - 3秒延迟等待AI完成当前回复
- 🔄 **自动退出** - 故事开始播放后自动退出AI聊天
- 🎛️ **灵活控制** - 支持立即播放、取消延迟等操作
- 📊 **状态透明** - 完善的状态监控和调试信息

现在小萝卜机器人的AI语音交互更加自然流畅，用户体验显著提升！🤖✨
